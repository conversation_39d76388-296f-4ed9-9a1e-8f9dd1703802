# 面向异构专业软件的服务化集成架构研究（续）

### 3.5 适配层实现技术

适配层的实现涉及多种技术和框架的选择与应用，本节将详细介绍适配层的核心实现技术。

#### 3.5.1 技术栈选择

在实现适配层时，我们综合考虑了功能需求、性能要求和技术成熟度等因素，选择了以下技术栈：

1. **基础框架**：Spring Boot作为基础框架，提供依赖注入、配置管理、Web服务等核心功能。

2. **集成框架**：Spring Integration作为集成框架，提供消息通道、适配器、转换器等集成组件。

3. **Web服务支持**：
   - RestTemplate和WebClient用于RESTful API调用
   - Spring WS用于SOAP服务调用
   - OkHttp用于高性能HTTP请求

4. **跨语言调用**：
   - JEP (Java Embedded Python)用于Java调用Python
   - Py4J用于Python调用Java
   - JNI (Java Native Interface)用于Java调用C/C++

5. **COM组件调用**：
   - Jacob (Java COM Bridge)用于Windows平台COM组件调用
   - JNA (Java Native Access)用于本地库调用

6. **数据处理**：
   - Jackson用于JSON处理
   - JAXB用于XML处理
   - Apache POI用于Office文档处理

7. **并发控制**：
   - CompletableFuture用于异步操作
   - ThreadPoolExecutor用于线程池管理

8. **缓存机制**：
   - Caffeine用于本地缓存
   - Redis用于分布式缓存

这些技术的组合使用，为适配层提供了强大的技术支持，满足了异构系统集成的各种需求。

#### 3.5.2 动态代理技术

为了简化适配器的使用，我们采用了动态代理技术，根据配置动态生成适配器实例。这种方式有以下优点：

1. **简化客户端代码**：客户端无需直接操作适配器实例，只需定义接口和调用方法。
2. **统一拦截处理**：可以在代理中统一处理日志、性能监控、缓存等横切关注点。
3. **运行时适配**：可以根据运行时条件选择不同的适配器实现。

动态代理实现代码示例：

```java
/**
 * 适配器代理工厂，负责创建适配器的动态代理
 */
public class AdapterProxyFactory {
    
    private final AdapterRegistry adapterRegistry;
    private final Map<String, Object> adapterProxies = new ConcurrentHashMap<>();
    
    public AdapterProxyFactory(AdapterRegistry adapterRegistry) {
        this.adapterRegistry = adapterRegistry;
    }
    
    /**
     * 创建适配器代理
     * @param adapterId 适配器ID
     * @param interfaceClass 接口类
     * @return 适配器代理
     */
    @SuppressWarnings("unchecked")
    public <T> T createProxy(String adapterId, Class<T> interfaceClass) {
        String proxyKey = adapterId + ":" + interfaceClass.getName();
        
        // 检查缓存
        if (adapterProxies.containsKey(proxyKey)) {
            return (T) adapterProxies.get(proxyKey);
        }
        
        // 获取适配器
        SoftwareAdapter adapter = adapterRegistry.getAdapter(adapterId);
        if (adapter == null) {
            throw new IllegalArgumentException("Adapter not found: " + adapterId);
        }
        
        // 创建代理
        T proxy = (T) Proxy.newProxyInstance(
            interfaceClass.getClassLoader(),
            new Class<?>[] { interfaceClass },
            new AdapterInvocationHandler(adapter)
        );
        
        // 缓存代理
        adapterProxies.put(proxyKey, proxy);
        
        return proxy;
    }
    
    /**
     * 适配器调用处理器
     */
    private static class AdapterInvocationHandler implements InvocationHandler {
        private final SoftwareAdapter adapter;
        private final Logger logger = LoggerFactory.getLogger(AdapterInvocationHandler.class);
        
        public AdapterInvocationHandler(SoftwareAdapter adapter) {
            this.adapter = adapter;
        }
        
        @Override
        public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
            // 处理Object类方法
            if (method.getDeclaringClass() == Object.class) {
                return method.invoke(this, args);
            }
            
            // 获取操作名称
            String operationName = method.getName();
            
            // 获取方法参数
            Map<String, Object> params = new HashMap<>();
            Parameter[] parameters = method.getParameters();
            for (int i = 0; i < parameters.length; i++) {
                params.put(parameters[i].getName(), args[i]);
            }
            
            // 记录调用日志
            logger.debug("Invoking operation: {} with params: {}", operationName, params);
            
            // 执行操作
            long startTime = System.currentTimeMillis();
            try {
                Result result = adapter.executeOperation(operationName, params);
                long endTime = System.currentTimeMillis();
                logger.debug("Operation completed in {}ms: {}", (endTime - startTime), result);
                
                // 转换结果
                return convertResult(result, method.getReturnType());
            } catch (AdapterException e) {
                logger.error("Operation failed: {}", e.getMessage(), e);
                throw e;
            }
        }
        
        /**
         * 转换结果为方法返回类型
         */
        private Object convertResult(Result result, Class<?> returnType) {
            if (result == null || !result.isSuccess()) {
                return null;
            }
            
            Object resultValue = result.getData().get("result");
            if (resultValue == null) {
                return null;
            }
            
            // 如果返回类型就是Result，直接返回
            if (returnType.isAssignableFrom(Result.class)) {
                return result;
            }
            
            // 如果返回类型是void，返回null
            if (returnType == void.class || returnType == Void.class) {
                return null;
            }
            
            // 转换基本类型
            if (returnType.isPrimitive() || Number.class.isAssignableFrom(returnType) ||
                returnType == String.class || returnType == Boolean.class) {
                return convertPrimitive(resultValue, returnType);
            }
            
            // 转换集合类型
            if (Collection.class.isAssignableFrom(returnType)) {
                return convertCollection(resultValue, returnType);
            }
            
            // 转换Map类型
            if (Map.class.isAssignableFrom(returnType)) {
                return convertMap(resultValue, returnType);
            }
            
            // 转换为复杂对象
            return convertObject(resultValue, returnType);
        }
        
        // 类型转换方法实现...
        private Object convertPrimitive(Object value, Class<?> type) {
            // 简化实现
            return value;
        }
        
        private Object convertCollection(Object value, Class<?> type) {
            // 简化实现
            return value;
        }
        
        private Object convertMap(Object value, Class<?> type) {
            // 简化实现
            return value;
        }
        
        private Object convertObject(Object value, Class<?> type) {
            // 简化实现
            return value;
        }
    }
}
```

使用动态代理的客户端代码示例：

```java
// 定义服务接口
public interface CadService {
    boolean openDocument(String filePath);
    boolean saveDocument(String filePath);
    boolean exportToPdf(String sourcePath, String targetPath);
}

// 使用适配器代理
CadService cadService = adapterProxyFactory.createProxy("cadAdapter", CadService.class);

// 调用服务方法
boolean result = cadService.openDocument("C:/designs/sample.dwg");
```

通过动态代理技术，我们实现了适配器的透明调用，大大简化了客户端代码，提高了开发效率。

#### 3.5.3 异步调用机制

对于耗时操作，同步调用可能导致线程阻塞和资源浪费。我们实现了异步调用机制，使用CompletableFuture支持非阻塞操作，提高系统响应性。

异步适配器接口定义：

```java
/**
 * 异步软件适配器接口
 */
public interface AsyncSoftwareAdapter extends SoftwareAdapter {
    
    /**
     * 异步执行操作
     * @param operationName 操作名称
     * @param params 操作参数
     * @return 操作结果的CompletableFuture
     */
    CompletableFuture<Result> executeOperationAsync(String operationName, Map<String, Object> params);
    
    /**
     * 取消操作
     * @param operationId 操作ID
     * @return 是否成功取消
     */
    boolean cancelOperation(String operationId);
}
```

异步适配器实现示例：

```java
/**
 * 异步适配器基类
 */
public abstract class BaseAsyncAdapter implements AsyncSoftwareAdapter {
    
    private final ExecutorService executorService;
    private final Map<String, CompletableFuture<Result>> pendingOperations = new ConcurrentHashMap<>();
    
    public BaseAsyncAdapter() {
        this.executorService = Executors.newCachedThreadPool();
    }
    
    @Override
    public CompletableFuture<Result> executeOperationAsync(String operationName, Map<String, Object> params) {
        String operationId = UUID.randomUUID().toString();
        
        CompletableFuture<Result> future = CompletableFuture.supplyAsync(() -> {
            try {
                // 执行同步操作
                return executeOperation(operationName, params);
            } catch (AdapterException e) {
                throw new CompletionException(e);
            } finally {
                // 操作完成后从待处理列表中移除
                pendingOperations.remove(operationId);
            }
        }, executorService);
        
        // 添加到待处理列表
        pendingOperations.put(operationId, future);
        
        return future;
    }
    
    @Override
    public boolean cancelOperation(String operationId) {
        CompletableFuture<Result> future = pendingOperations.get(operationId);
        if (future != null && !future.isDone()) {
            boolean cancelled = future.cancel(true);
            if (cancelled) {
                pendingOperations.remove(operationId);
            }
            return cancelled;
        }
        return false;
    }
    
    @Override
    public Result getOperationResult(String operationId) throws AdapterException {
        CompletableFuture<Result> future = pendingOperations.get(operationId);
        if (future == null) {
            throw new OperationException("Operation not found: " + operationId);
        }
        
        if (future.isDone()) {
            try {
                return future.get();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new OperationException("Operation interrupted: " + operationId, e);
            } catch (ExecutionException e) {
                Throwable cause = e.getCause();
                if (cause instanceof AdapterException) {
                    throw (AdapterException) cause;
                }
                throw new OperationException("Operation failed: " + operationId, cause);
            }
        } else {
            // 操作仍在进行中
            return Result.processing(operationId);
        }
    }
    
    @Override
    public void disconnect() throws AdapterException {
        // 关闭执行器
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            executorService.shutdownNow();
        }
    }
}
```

异步调用的客户端代码示例：

```java
// 获取异步适配器
AsyncSoftwareAdapter asyncAdapter = adapterRegistry.getAsyncAdapter("longRunningAdapter");

// 异步执行操作
CompletableFuture<Result> future = asyncAdapter.executeOperationAsync("processLargeData", params);

// 添加回调处理
future.thenAccept(result -> {
    if (result.isSuccess()) {
        System.out.println("Operation completed successfully: " + result.getData());
    } else {
        System.err.println("Operation failed: " + result.getMessage());
    }
});

// 异步组合操作
CompletableFuture<Result> future1 = asyncAdapter.executeOperationAsync("operation1", params1);
CompletableFuture<Result> future2 = asyncAdapter.executeOperationAsync("operation2", params2);

CompletableFuture.allOf(future1, future2)
    .thenRun(() -> {
        System.out.println("All operations completed");
    });
```

通过异步调用机制，我们实现了对耗时操作的高效处理，避免了线程阻塞，提高了系统的并发处理能力和响应性。
