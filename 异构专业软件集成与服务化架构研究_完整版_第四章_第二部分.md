# 面向异构专业软件的服务化集成架构研究（续）

#### 4.1.2 任务队列管理

任务队列管理是调度层的核心功能，负责管理待执行的任务，确保任务的有序执行和资源的合理分配。我们设计了多级队列结构，支持任务优先级、超时控制和重试策略等高级特性。

**1. 任务模型设计**

任务是调度系统的基本执行单元，我们设计了完整的任务模型，包括任务属性、状态和生命周期。

```java
/**
 * 任务类，表示一个待执行的操作
 */
public class Task implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private String id;                     // 任务ID
    private String name;                   // 任务名称
    private String description;            // 任务描述
    private String adapterId;              // 目标适配器ID
    private String operationName;          // 操作名称
    private Map<String, Object> params;    // 操作参数
    private int priority;                  // 优先级(1-10)
    private TaskStatus status;             // 任务状态
    private long createTime;               // 创建时间
    private long startTime;                // 开始执行时间
    private long endTime;                  // 结束执行时间
    private long timeoutMs;                // 超时时间(毫秒)
    private int maxRetries;                // 最大重试次数
    private int retryCount;                // 当前重试次数
    private long retryDelayMs;             // 重试延迟时间(毫秒)
    private String userId;                 // 创建用户ID
    private String groupId;                // 任务组ID
    private Map<String, Object> tags;      // 标签
    private Result result;                 // 执行结果
    private String errorMessage;           // 错误信息
    
    // 构造函数、getters和setters...
    
    /**
     * 创建任务构建器
     */
    public static Builder builder() {
        return new Builder();
    }
    
    /**
     * 任务构建器
     */
    public static class Builder {
        private final Task task;
        
        public Builder() {
            task = new Task();
            task.id = UUID.randomUUID().toString();
            task.createTime = System.currentTimeMillis();
            task.status = TaskStatus.CREATED;
            task.priority = 5; // 默认优先级
            task.params = new HashMap<>();
            task.tags = new HashMap<>();
            task.maxRetries = 0;
            task.retryCount = 0;
            task.retryDelayMs = 0;
            task.timeoutMs = 0; // 默认不超时
        }
        
        public Builder name(String name) {
            task.name = name;
            return this;
        }
        
        public Builder description(String description) {
            task.description = description;
            return this;
        }
        
        public Builder adapterId(String adapterId) {
            task.adapterId = adapterId;
            return this;
        }
        
        public Builder operationName(String operationName) {
            task.operationName = operationName;
            return this;
        }
        
        public Builder params(Map<String, Object> params) {
            task.params.putAll(params);
            return this;
        }
        
        public Builder param(String key, Object value) {
            task.params.put(key, value);
            return this;
        }
        
        public Builder priority(int priority) {
            task.priority = Math.max(1, Math.min(10, priority)); // 限制在1-10范围内
            return this;
        }
        
        public Builder timeout(long timeout, TimeUnit unit) {
            task.timeoutMs = unit.toMillis(timeout);
            return this;
        }
        
        public Builder retry(int maxRetries, long retryDelay, TimeUnit unit) {
            task.maxRetries = maxRetries;
            task.retryDelayMs = unit.toMillis(retryDelay);
            return this;
        }
        
        public Builder userId(String userId) {
            task.userId = userId;
            return this;
        }
        
        public Builder groupId(String groupId) {
            task.groupId = groupId;
            return this;
        }
        
        public Builder tag(String key, Object value) {
            task.tags.put(key, value);
            return this;
        }
        
        public Task build() {
            // 验证必要字段
            if (task.adapterId == null || task.adapterId.isEmpty()) {
                throw new IllegalStateException("Adapter ID is required");
            }
            if (task.operationName == null || task.operationName.isEmpty()) {
                throw new IllegalStateException("Operation name is required");
            }
            
            return task;
        }
    }
}

/**
 * 任务状态枚举
 */
public enum TaskStatus {
    CREATED,        // 已创建
    QUEUED,         // 已加入队列
    SCHEDULED,      // 已调度
    RUNNING,        // 执行中
    COMPLETED,      // 已完成
    FAILED,         // 失败
    CANCELLED,      // 已取消
    TIMEOUT,        // 超时
    RETRY_SCHEDULED // 重试已调度
}
```

任务模型设计考虑了以下关键点：

- **任务标识**：每个任务有唯一ID，便于跟踪和管理。
- **任务属性**：包含名称、描述、目标适配器、操作名称、参数等基本属性。
- **执行控制**：包含优先级、超时时间、重试策略等执行控制属性。
- **状态跟踪**：记录任务的当前状态、创建时间、开始时间、结束时间等。
- **结果处理**：存储执行结果和错误信息。
- **分组和标签**：支持任务分组和自定义标签，便于批量管理和查询。

**2. 多级队列设计**

为了支持不同优先级和类型的任务，我们设计了多级队列结构，包括全局队列、专用队列和延迟队列。

```java
/**
 * 任务队列管理器
 */
public class TaskQueueManager {
    private final PriorityBlockingQueue<Task> globalQueue;
    private final Map<String, PriorityBlockingQueue<Task>> adapterQueues;
    private final DelayQueue<DelayedTask> delayQueue;
    private final TaskRepository taskRepository;
    private final ApplicationEventPublisher eventPublisher;
    private final Logger logger = LoggerFactory.getLogger(TaskQueueManager.class);
    
    public TaskQueueManager(TaskRepository taskRepository, ApplicationEventPublisher eventPublisher) {
        this.taskRepository = taskRepository;
        this.eventPublisher = eventPublisher;
        
        // 创建全局优先级队列
        this.globalQueue = new PriorityBlockingQueue<>(1000, Comparator.comparingInt(Task::getPriority).reversed());
        
        // 创建适配器专用队列
        this.adapterQueues = new ConcurrentHashMap<>();
        
        // 创建延迟队列
        this.delayQueue = new DelayQueue<>();
    }
    
    /**
     * 提交任务
     */
    public Task submitTask(Task task) {
        logger.debug("Submitting task: {}", task.getId());
        
        // 更新任务状态
        task.setStatus(TaskStatus.QUEUED);
        
        // 保存任务到数据库
        taskRepository.save(task);
        
        // 加入全局队列
        globalQueue.offer(task);
        
        // 发布任务提交事件
        publishTaskEvent(new TaskEvent(this, TaskEventType.SUBMITTED, task));
        
        logger.info("Task submitted: {}", task.getId());
        return task;
    }
    
    /**
     * 提交延迟任务
     */
    public Task submitDelayedTask(Task task, long delay, TimeUnit unit) {
        logger.debug("Submitting delayed task: {}, delay: {}", task.getId(), delay);
        
        // 更新任务状态
        task.setStatus(TaskStatus.QUEUED);
        
        // 保存任务到数据库
        taskRepository.save(task);
        
        // 创建延迟任务
        DelayedTask delayedTask = new DelayedTask(task, unit.toMillis(delay));
        
        // 加入延迟队列
        delayQueue.offer(delayedTask);
        
        // 发布任务提交事件
        publishTaskEvent(new TaskEvent(this, TaskEventType.DELAYED, task));
        
        logger.info("Delayed task submitted: {}, delay: {}", task.getId(), delay);
        return task;
    }
    
    /**
     * 从全局队列获取下一个任务
     */
    public Task pollNextTask() {
        return globalQueue.poll();
    }
    
    /**
     * 从适配器队列获取下一个任务
     */
    public Task pollNextTask(String adapterId) {
        PriorityBlockingQueue<Task> adapterQueue = adapterQueues.get(adapterId);
        if (adapterQueue != null) {
            return adapterQueue.poll();
        }
        return null;
    }
    
    /**
     * 从延迟队列获取到期任务
     */
    public List<Task> pollExpiredDelayedTasks() {
        List<Task> expiredTasks = new ArrayList<>();
        DelayedTask delayedTask;
        
        // 获取所有到期的延迟任务
        while ((delayedTask = delayQueue.poll()) != null) {
            expiredTasks.add(delayedTask.getTask());
        }
        
        return expiredTasks;
    }
    
    /**
     * 将任务分配到适配器队列
     */
    public void assignTaskToAdapter(Task task, String adapterId) {
        logger.debug("Assigning task {} to adapter {}", task.getId(), adapterId);
        
        // 获取或创建适配器队列
        PriorityBlockingQueue<Task> adapterQueue = adapterQueues.computeIfAbsent(
                adapterId,
                k -> new PriorityBlockingQueue<>(100, Comparator.comparingInt(Task::getPriority).reversed())
        );
        
        // 更新任务状态和适配器ID
        task.setStatus(TaskStatus.SCHEDULED);
        task.setAdapterId(adapterId);
        
        // 保存任务到数据库
        taskRepository.save(task);
        
        // 加入适配器队列
        adapterQueue.offer(task);
        
        // 发布任务分配事件
        publishTaskEvent(new TaskEvent(this, TaskEventType.ASSIGNED, task));
        
        logger.info("Task {} assigned to adapter {}", task.getId(), adapterId);
    }
    
    /**
     * 取消任务
     */
    public boolean cancelTask(String taskId) {
        logger.debug("Cancelling task: {}", taskId);
        
        // 从数据库获取任务
        Task task = taskRepository.findById(taskId).orElse(null);
        if (task == null) {
            logger.warn("Task not found for cancellation: {}", taskId);
            return false;
        }
        
        // 检查任务状态
        if (task.getStatus() == TaskStatus.COMPLETED || 
            task.getStatus() == TaskStatus.FAILED ||
            task.getStatus() == TaskStatus.CANCELLED) {
            logger.warn("Cannot cancel task in state: {}", task.getStatus());
            return false;
        }
        
        // 更新任务状态
        task.setStatus(TaskStatus.CANCELLED);
        
        // 保存任务到数据库
        taskRepository.save(task);
        
        // 发布任务取消事件
        publishTaskEvent(new TaskEvent(this, TaskEventType.CANCELLED, task));
        
        logger.info("Task cancelled: {}", taskId);
        return true;
    }
    
    /**
     * 获取队列统计信息
     */
    public QueueStats getQueueStats() {
        QueueStats stats = new QueueStats();
        stats.setGlobalQueueSize(globalQueue.size());
        
        Map<String, Integer> adapterQueueSizes = new HashMap<>();
        for (Map.Entry<String, PriorityBlockingQueue<Task>> entry : adapterQueues.entrySet()) {
            adapterQueueSizes.put(entry.getKey(), entry.getValue().size());
        }
        stats.setAdapterQueueSizes(adapterQueueSizes);
        
        stats.setDelayQueueSize(delayQueue.size());
        
        return stats;
    }
    
    /**
     * 发布任务事件
     */
    private void publishTaskEvent(TaskEvent event) {
        eventPublisher.publishEvent(event);
    }
    
    /**
     * 延迟任务包装类
     */
    private static class DelayedTask implements Delayed {
        private final Task task;
        private final long expireTimeMs;
        
        public DelayedTask(Task task, long delayMs) {
            this.task = task;
            this.expireTimeMs = System.currentTimeMillis() + delayMs;
        }
        
        public Task getTask() {
            return task;
        }
        
        @Override
        public long getDelay(TimeUnit unit) {
            long diff = expireTimeMs - System.currentTimeMillis();
            return unit.convert(diff, TimeUnit.MILLISECONDS);
        }
        
        @Override
        public int compareTo(Delayed other) {
            if (other == this) {
                return 0;
            }
            long diff = getDelay(TimeUnit.MILLISECONDS) - other.getDelay(TimeUnit.MILLISECONDS);
            return Long.compare(diff, 0);
        }
    }
    
    /**
     * 队列统计信息类
     */
    public static class QueueStats {
        private int globalQueueSize;
        private Map<String, Integer> adapterQueueSizes;
        private int delayQueueSize;
        
        // getters和setters...
    }
}
```

多级队列设计考虑了以下关键点：

- **全局优先级队列**：存储所有待执行的任务，按优先级排序。
- **适配器专用队列**：为每个适配应用设置专用队列，避免单一应用占用过多资源。
- **延迟队列**：存储需要延迟执行的任务，如重试任务、定时任务等。
- **队列操作**：支持任务提交、获取、分配和取消等基本操作。
- **事件通知**：任务状态变更时发布事件，便于其他组件感知和处理。
- **统计信息**：提供队列状态统计，便于监控和管理。

**3. 任务调度策略**

任务调度策略决定了如何从队列中选择任务并分配给适配应用执行，是调度层的核心决策逻辑。

```java
/**
 * 任务调度器
 */
public class TaskScheduler {
    private final TaskQueueManager queueManager;
    private final AdapterRegistry adapterRegistry;
    private final TaskExecutor taskExecutor;
    private final ScheduledExecutorService scheduler;
    private final TaskRepository taskRepository;
    private final ApplicationEventPublisher eventPublisher;
    private final Logger logger = LoggerFactory.getLogger(TaskScheduler.class);
    
    private volatile boolean running = false;
    
    public TaskScheduler(TaskQueueManager queueManager, AdapterRegistry adapterRegistry,
                         TaskExecutor taskExecutor, TaskRepository taskRepository,
                         ApplicationEventPublisher eventPublisher) {
        this.queueManager = queueManager;
        this.adapterRegistry = adapterRegistry;
        this.taskExecutor = taskExecutor;
        this.taskRepository = taskRepository;
        this.eventPublisher = eventPublisher;
        this.scheduler = Executors.newScheduledThreadPool(2);
    }
    
    /**
     * 启动调度器
     */
    public void start() {
        if (running) {
            return;
        }
        
        logger.info("Starting task scheduler");
        running = true;
        
        // 启动主调度循环
        scheduler.scheduleWithFixedDelay(this::scheduleTasks, 0, 100, TimeUnit.MILLISECONDS);
        
        // 启动延迟任务处理
        scheduler.scheduleWithFixedDelay(this::processDelayedTasks, 0, 1000, TimeUnit.MILLISECONDS);
        
        logger.info("Task scheduler started");
    }
    
    /**
     * 停止调度器
     */
    public void stop() {
        if (!running) {
            return;
        }
        
        logger.info("Stopping task scheduler");
        running = false;
        
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            scheduler.shutdownNow();
        }
        
        logger.info("Task scheduler stopped");
    }
    
    /**
     * 主调度循环
     */
    private void scheduleTasks() {
        try {
            // 获取可用适配器
            List<String> availableAdapters = getAvailableAdapters();
            if (availableAdapters.isEmpty()) {
                logger.debug("No available adapters for scheduling");
                return;
            }
            
            // 获取下一个任务
            Task task = queueManager.pollNextTask();
            if (task == null) {
                logger.debug("No tasks in queue for scheduling");
                return;
            }
            
            // 选择适配器
            String adapterId = selectAdapter(task, availableAdapters);
            if (adapterId == null) {
                // 无法找到合适的适配器，将任务重新放回队列
                logger.warn("No suitable adapter found for task {}, requeuing", task.getId());
                queueManager.submitTask(task);
                return;
            }
            
            // 分配任务到适配器
            queueManager.assignTaskToAdapter(task, adapterId);
            
            // 执行任务
            executeTask(task);
        } catch (Exception e) {
            logger.error("Error in task scheduling", e);
        }
    }
    
    /**
     * 处理延迟任务
     */
    private void processDelayedTasks() {
        try {
            // 获取到期的延迟任务
            List<Task> expiredTasks = queueManager.pollExpiredDelayedTasks();
            
            for (Task task : expiredTasks) {
                logger.debug("Processing expired delayed task: {}", task.getId());
                
                // 将任务提交到全局队列
                queueManager.submitTask(task);
            }
        } catch (Exception e) {
            logger.error("Error in processing delayed tasks", e);
        }
    }
    
    /**
     * 获取可用适配器列表
     */
    private List<String> getAvailableAdapters() {
        return adapterRegistry.getAllAdapterIds().stream()
                .filter(id -> adapterRegistry.getAdapterStatus(id) == AdapterStatus.AVAILABLE)
                .collect(Collectors.toList());
    }
    
    /**
     * 选择适配器
     */
    private String selectAdapter(Task task, List<String> availableAdapters) {
        // 如果任务指定了适配器，且该适配器可用，则使用指定适配器
        String preferredAdapter = task.getAdapterId();
        if (preferredAdapter != null && availableAdapters.contains(preferredAdapter)) {
            return preferredAdapter;
        }
        
        // 否则，根据负载均衡策略选择适配器
        return selectAdapterByLoadBalancing(availableAdapters);
    }
    
    /**
     * 根据负载均衡策略选择适配器
     */
    private String selectAdapterByLoadBalancing(List<String> availableAdapters) {
        if (availableAdapters.isEmpty()) {
            return null;
        }
        
        // 简单的轮询策略
        // 实际实现可能更复杂，考虑负载、响应时间等因素
        return availableAdapters.get(0);
    }
    
    /**
     * 执行任务
     */
    private void executeTask(Task task) {
        logger.debug("Executing task: {}", task.getId());
        
        // 更新任务状态
        task.setStatus(TaskStatus.RUNNING);
        task.setStartTime(System.currentTimeMillis());
        taskRepository.save(task);
        
        // 发布任务开始事件
        publishTaskEvent(new TaskEvent(this, TaskEventType.STARTED, task));
        
        // 异步执行任务
        CompletableFuture.runAsync(() -> {
            try {
                // 执行任务
                Result result = taskExecutor.executeTask(task);
                
                // 处理执行结果
                handleTaskResult(task, result);
            } catch (Exception e) {
                // 处理执行异常
                handleTaskException(task, e);
            }
        });
    }
    
    /**
     * 处理任务执行结果
     */
    private void handleTaskResult(Task task, Result result) {
        logger.debug("Handling task result: {}, success: {}", task.getId(), result.isSuccess());
        
        // 更新任务状态和结果
        task.setEndTime(System.currentTimeMillis());
        task.setResult(result);
        
        if (result.isSuccess()) {
            // 任务成功
            task.setStatus(TaskStatus.COMPLETED);
            taskRepository.save(task);
            
            // 发布任务完成事件
            publishTaskEvent(new TaskEvent(this, TaskEventType.COMPLETED, task));
        } else {
            // 任务失败
            task.setStatus(TaskStatus.FAILED);
            task.setErrorMessage(result.getMessage());
            
            // 检查是否需要重试
            if (task.getRetryCount() < task.getMaxRetries()) {
                // 安排重试
                scheduleRetry(task);
            } else {
                // 达到最大重试次数，标记为最终失败
                taskRepository.save(task);
                
                // 发布任务失败事件
                publishTaskEvent(new TaskEvent(this, TaskEventType.FAILED, task));
            }
        }
    }
    
    /**
     * 处理任务执行异常
     */
    private void handleTaskException(Task task, Exception e) {
        logger.error("Task execution failed: {}", task.getId(), e);
        
        // 更新任务状态
        task.setEndTime(System.currentTimeMillis());
        task.setStatus(TaskStatus.FAILED);
        task.setErrorMessage(e.getMessage());
        
        // 检查是否需要重试
        if (task.getRetryCount() < task.getMaxRetries()) {
            // 安排重试
            scheduleRetry(task);
        } else {
            // 达到最大重试次数，标记为最终失败
            taskRepository.save(task);
            
            // 发布任务失败事件
            publishTaskEvent(new TaskEvent(this, TaskEventType.FAILED, task));
        }
    }
    
    /**
     * 安排任务重试
     */
    private void scheduleRetry(Task task) {
        logger.debug("Scheduling retry for task: {}, retry count: {}", task.getId(), task.getRetryCount());
        
        // 增加重试计数
        task.setRetryCount(task.getRetryCount() + 1);
        
        // 更新任务状态
        task.setStatus(TaskStatus.RETRY_SCHEDULED);
        taskRepository.save(task);
        
        // 计算重试延迟时间（指数退避策略）
        long retryDelay = calculateRetryDelay(task);
        
        // 提交延迟任务
        queueManager.submitDelayedTask(task, retryDelay, TimeUnit.MILLISECONDS);
        
        // 发布任务重试事件
        publishTaskEvent(new TaskEvent(this, TaskEventType.RETRY_SCHEDULED, task));
    }
    
    /**
     * 计算重试延迟时间（指数退避策略）
     */
    private long calculateRetryDelay(Task task) {
        // 基础延迟时间
        long baseDelay = task.getRetryDelayMs();
        
        // 如果未设置基础延迟，使用默认值
        if (baseDelay <= 0) {
            baseDelay = 1000; // 1秒
        }
        
        // 指数退避：延迟时间 = 基础延迟 * (2^重试次数)
        return baseDelay * (long) Math.pow(2, task.getRetryCount() - 1);
    }
    
    /**
     * 发布任务事件
     */
    private void publishTaskEvent(TaskEvent event) {
        eventPublisher.publishEvent(event);
    }
}
```

任务调度策略考虑了以下关键点：

- **调度循环**：定期从队列中获取任务并分配给适配应用执行。
- **适配器选择**：根据任务要求和适配应用状态选择合适的执行节点。
- **任务执行**：异步执行任务，避免阻塞调度线程。
- **结果处理**：处理任务执行结果，更新任务状态和记录。
- **重试机制**：对失败任务实现指数退避重试策略，提高任务成功率。
- **事件通知**：任务状态变更时发布事件，便于其他组件感知和处理。
