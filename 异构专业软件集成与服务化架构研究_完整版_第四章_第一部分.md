# 面向异构专业软件的服务化集成架构研究（续）

## 4. 调度层设计与实现

调度层是整个服务化集成架构的核心控制中心，负责监听和管理各个适配应用，协调任务的分配和执行，确保系统的高效运行。本章将详细介绍调度层的设计思路、关键技术和实现方法。

### 4.1 调度层功能设计

调度层作为连接适配层和组件封装层的中间层，承担着资源管理和任务调度的重要职责。本节将详细介绍调度层的核心功能设计。

#### 4.1.1 服务监听机制

服务监听机制负责实时监控各个适配应用的状态，及时发现服务异常并进行处理。我们设计了多级监听机制，确保服务状态的准确感知。

**1. 心跳检测机制**

心跳检测是监控服务状态的基础机制，要求各适配应用定期发送心跳信息，调度层通过心跳超时判断服务是否可用。

```java
/**
 * 心跳检测服务
 */
public class HeartbeatMonitorService {
    private final Map<String, AdapterStatus> adapterStatusMap = new ConcurrentHashMap<>();
    private final Map<String, Long> lastHeartbeatTimeMap = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    private final long heartbeatTimeoutMs;
    private final ApplicationEventPublisher eventPublisher;
    private final Logger logger = LoggerFactory.getLogger(HeartbeatMonitorService.class);
    
    public HeartbeatMonitorService(long heartbeatTimeoutMs, ApplicationEventPublisher eventPublisher) {
        this.heartbeatTimeoutMs = heartbeatTimeoutMs;
        this.eventPublisher = eventPublisher;
    }
    
    /**
     * 启动心跳监控
     */
    public void start() {
        logger.info("Starting heartbeat monitor service");
        
        // 定期检查心跳超时
        scheduler.scheduleAtFixedRate(this::checkHeartbeats, 0, heartbeatTimeoutMs / 2, TimeUnit.MILLISECONDS);
    }
    
    /**
     * 停止心跳监控
     */
    public void stop() {
        logger.info("Stopping heartbeat monitor service");
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            scheduler.shutdownNow();
        }
    }
    
    /**
     * 接收适配器心跳
     */
    public void receiveHeartbeat(String adapterId, AdapterStatus status) {
        logger.debug("Received heartbeat from adapter: {}, status: {}", adapterId, status);
        
        long currentTime = System.currentTimeMillis();
        AdapterStatus oldStatus = adapterStatusMap.get(adapterId);
        
        // 更新心跳时间和状态
        lastHeartbeatTimeMap.put(adapterId, currentTime);
        adapterStatusMap.put(adapterId, status);
        
        // 如果状态发生变化，发布状态变更事件
        if (oldStatus != status) {
            logger.info("Adapter status changed: {} -> {}", oldStatus, status);
            publishStatusChangeEvent(adapterId, oldStatus, status);
        }
    }
    
    /**
     * 检查心跳超时
     */
    private void checkHeartbeats() {
        long currentTime = System.currentTimeMillis();
        
        for (Map.Entry<String, Long> entry : lastHeartbeatTimeMap.entrySet()) {
            String adapterId = entry.getKey();
            long lastHeartbeatTime = entry.getValue();
            
            // 检查是否超时
            if (currentTime - lastHeartbeatTime > heartbeatTimeoutMs) {
                AdapterStatus oldStatus = adapterStatusMap.get(adapterId);
                
                // 如果当前状态不是不可用，则更新为不可用
                if (oldStatus != AdapterStatus.UNAVAILABLE) {
                    logger.warn("Heartbeat timeout for adapter: {}", adapterId);
                    adapterStatusMap.put(adapterId, AdapterStatus.UNAVAILABLE);
                    publishStatusChangeEvent(adapterId, oldStatus, AdapterStatus.UNAVAILABLE);
                }
            }
        }
    }
    
    /**
     * 发布状态变更事件
     */
    private void publishStatusChangeEvent(String adapterId, AdapterStatus oldStatus, AdapterStatus newStatus) {
        AdapterStatusChangeEvent event = new AdapterStatusChangeEvent(this, adapterId, oldStatus, newStatus);
        eventPublisher.publishEvent(event);
    }
    
    /**
     * 获取适配器状态
     */
    public AdapterStatus getAdapterStatus(String adapterId) {
        return adapterStatusMap.getOrDefault(adapterId, AdapterStatus.UNKNOWN);
    }
    
    /**
     * 获取所有适配器状态
     */
    public Map<String, AdapterStatus> getAllAdapterStatus() {
        return new HashMap<>(adapterStatusMap);
    }
}
```

心跳检测机制通过定期接收适配应用的心跳信息，实时更新服务状态，并在心跳超时时自动将服务标记为不可用，确保了服务状态监控的及时性和准确性。

**2. 主动探测机制**

除了被动接收心跳外，调度层还会主动向各适配应用发送探测请求，验证服务的可用性和响应时间。

```java
/**
 * 主动探测服务
 */
public class ActiveProbeService {
    private final AdapterRegistry adapterRegistry;
    private final Map<String, ProbeResult> probeResultMap = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    private final long probeIntervalMs;
    private final ApplicationEventPublisher eventPublisher;
    private final Logger logger = LoggerFactory.getLogger(ActiveProbeService.class);
    
    public ActiveProbeService(AdapterRegistry adapterRegistry, long probeIntervalMs, ApplicationEventPublisher eventPublisher) {
        this.adapterRegistry = adapterRegistry;
        this.probeIntervalMs = probeIntervalMs;
        this.eventPublisher = eventPublisher;
    }
    
    /**
     * 启动主动探测
     */
    public void start() {
        logger.info("Starting active probe service");
        
        // 定期执行探测
        scheduler.scheduleAtFixedRate(this::probeAllAdapters, 0, probeIntervalMs, TimeUnit.MILLISECONDS);
    }
    
    /**
     * 停止主动探测
     */
    public void stop() {
        logger.info("Stopping active probe service");
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            scheduler.shutdownNow();
        }
    }
    
    /**
     * 探测所有适配器
     */
    private void probeAllAdapters() {
        logger.debug("Probing all adapters");
        
        List<String> adapterIds = adapterRegistry.getAllAdapterIds();
        for (String adapterId : adapterIds) {
            CompletableFuture.runAsync(() -> probeAdapter(adapterId));
        }
    }
    
    /**
     * 探测单个适配器
     */
    private void probeAdapter(String adapterId) {
        try {
            logger.debug("Probing adapter: {}", adapterId);
            
            long startTime = System.currentTimeMillis();
            boolean available = false;
            String message = null;
            
            try {
                // 获取适配器
                SoftwareAdapter adapter = adapterRegistry.getAdapter(adapterId);
                if (adapter != null) {
                    // 执行健康检查操作
                    Result result = adapter.executeOperation("healthCheck", Collections.emptyMap());
                    available = result.isSuccess();
                    message = result.getMessage();
                } else {
                    message = "Adapter not found";
                }
            } catch (Exception e) {
                message = e.getMessage();
            }
            
            long endTime = System.currentTimeMillis();
            long responseTime = endTime - startTime;
            
            // 创建探测结果
            ProbeResult oldResult = probeResultMap.get(adapterId);
            ProbeResult newResult = new ProbeResult(adapterId, available, responseTime, message, endTime);
            probeResultMap.put(adapterId, newResult);
            
            // 如果可用性发生变化，发布状态变更事件
            if (oldResult == null || oldResult.isAvailable() != available) {
                AdapterStatus oldStatus = oldResult != null && oldResult.isAvailable() ? 
                        AdapterStatus.AVAILABLE : AdapterStatus.UNAVAILABLE;
                AdapterStatus newStatus = available ? AdapterStatus.AVAILABLE : AdapterStatus.UNAVAILABLE;
                
                logger.info("Adapter availability changed: {} -> {}", oldStatus, newStatus);
                publishStatusChangeEvent(adapterId, oldStatus, newStatus);
            }
            
            logger.debug("Probe result for adapter {}: available={}, responseTime={}ms", 
                    adapterId, available, responseTime);
        } catch (Exception e) {
            logger.error("Failed to probe adapter: {}", adapterId, e);
        }
    }
    
    /**
     * 发布状态变更事件
     */
    private void publishStatusChangeEvent(String adapterId, AdapterStatus oldStatus, AdapterStatus newStatus) {
        AdapterStatusChangeEvent event = new AdapterStatusChangeEvent(this, adapterId, oldStatus, newStatus);
        eventPublisher.publishEvent(event);
    }
    
    /**
     * 获取探测结果
     */
    public ProbeResult getProbeResult(String adapterId) {
        return probeResultMap.get(adapterId);
    }
    
    /**
     * 获取所有探测结果
     */
    public Map<String, ProbeResult> getAllProbeResults() {
        return new HashMap<>(probeResultMap);
    }
    
    /**
     * 探测结果类
     */
    public static class ProbeResult {
        private final String adapterId;
        private final boolean available;
        private final long responseTimeMs;
        private final String message;
        private final long timestamp;
        
        public ProbeResult(String adapterId, boolean available, long responseTimeMs, String message, long timestamp) {
            this.adapterId = adapterId;
            this.available = available;
            this.responseTimeMs = responseTimeMs;
            this.message = message;
            this.timestamp = timestamp;
        }
        
        // getters...
    }
}
```

主动探测机制通过定期向适配应用发送健康检查请求，测量响应时间和可用性，为服务状态监控提供了更主动的手段，弥补了心跳检测的不足。

**3. 事件订阅机制**

事件订阅机制允许适配应用主动推送状态变更事件，调度层实时接收并处理，实现了更及时的状态感知。

```java
/**
 * 适配器事件订阅服务
 */
public class AdapterEventSubscriptionService {
    private final Map<String, List<AdapterEvent>> adapterEventsMap = new ConcurrentHashMap<>();
    private final ApplicationEventPublisher eventPublisher;
    private final Logger logger = LoggerFactory.getLogger(AdapterEventSubscriptionService.class);
    
    public AdapterEventSubscriptionService(ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
    }
    
    /**
     * 接收适配器事件
     */
    public void receiveEvent(AdapterEvent event) {
        logger.debug("Received event from adapter {}: {}", event.getAdapterId(), event.getType());
        
        // 存储事件
        adapterEventsMap.computeIfAbsent(event.getAdapterId(), k -> new ArrayList<>())
                .add(event);
        
        // 限制事件历史数量
        List<AdapterEvent> events = adapterEventsMap.get(event.getAdapterId());
        if (events.size() > 100) {
            events.subList(0, events.size() - 100).clear();
        }
        
        // 处理特定类型的事件
        switch (event.getType()) {
            case STATUS_CHANGE:
                handleStatusChangeEvent(event);
                break;
            case RESOURCE_CHANGE:
                handleResourceChangeEvent(event);
                break;
            case ERROR:
                handleErrorEvent(event);
                break;
            default:
                // 其他事件类型处理...
                break;
        }
        
        // 发布事件到系统
        publishSystemEvent(event);
    }
    
    /**
     * 处理状态变更事件
     */
    private void handleStatusChangeEvent(AdapterEvent event) {
        @SuppressWarnings("unchecked")
        Map<String, Object> eventData = (Map<String, Object>) event.getData();
        AdapterStatus newStatus = AdapterStatus.valueOf((String) eventData.get("newStatus"));
        AdapterStatus oldStatus = AdapterStatus.valueOf((String) eventData.get("oldStatus"));
        
        logger.info("Adapter status changed: {} -> {}", oldStatus, newStatus);
        
        // 发布状态变更事件
        AdapterStatusChangeEvent statusEvent = new AdapterStatusChangeEvent(
                this, event.getAdapterId(), oldStatus, newStatus);
        eventPublisher.publishEvent(statusEvent);
    }
    
    /**
     * 处理资源变更事件
     */
    private void handleResourceChangeEvent(AdapterEvent event) {
        @SuppressWarnings("unchecked")
        Map<String, Object> eventData = (Map<String, Object>) event.getData();
        @SuppressWarnings("unchecked")
        Map<String, Object> resources = (Map<String, Object>) eventData.get("resources");
        
        logger.info("Adapter resources changed: {}", resources);
        
        // 发布资源变更事件
        AdapterResourceChangeEvent resourceEvent = new AdapterResourceChangeEvent(
                this, event.getAdapterId(), resources);
        eventPublisher.publishEvent(resourceEvent);
    }
    
    /**
     * 处理错误事件
     */
    private void handleErrorEvent(AdapterEvent event) {
        @SuppressWarnings("unchecked")
        Map<String, Object> eventData = (Map<String, Object>) event.getData();
        String errorCode = (String) eventData.get("errorCode");
        String errorMessage = (String) eventData.get("errorMessage");
        
        logger.error("Adapter error: {} - {}", errorCode, errorMessage);
        
        // 发布错误事件
        AdapterErrorEvent errorEvent = new AdapterErrorEvent(
                this, event.getAdapterId(), errorCode, errorMessage);
        eventPublisher.publishEvent(errorEvent);
    }
    
    /**
     * 发布系统事件
     */
    private void publishSystemEvent(AdapterEvent event) {
        SystemAdapterEvent systemEvent = new SystemAdapterEvent(this, event);
        eventPublisher.publishEvent(systemEvent);
    }
    
    /**
     * 获取适配器事件历史
     */
    public List<AdapterEvent> getAdapterEvents(String adapterId) {
        return adapterEventsMap.getOrDefault(adapterId, Collections.emptyList());
    }
    
    /**
     * 适配器事件类
     */
    public static class AdapterEvent {
        private final String adapterId;
        private final EventType type;
        private final Object data;
        private final long timestamp;
        
        public AdapterEvent(String adapterId, EventType type, Object data) {
            this.adapterId = adapterId;
            this.type = type;
            this.data = data;
            this.timestamp = System.currentTimeMillis();
        }
        
        // getters...
    }
    
    /**
     * 事件类型枚举
     */
    public enum EventType {
        STATUS_CHANGE,
        RESOURCE_CHANGE,
        ERROR,
        INFO,
        WARNING
    }
}
```

事件订阅机制通过接收适配应用主动推送的事件，实现了更丰富的状态监控，不仅包括可用性状态，还包括资源状态、错误信息等，为调度决策提供了更全面的依据。
