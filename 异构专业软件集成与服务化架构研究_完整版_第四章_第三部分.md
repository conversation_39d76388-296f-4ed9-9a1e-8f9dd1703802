# 面向异构专业软件的服务化集成架构研究（续）

#### 4.1.3 负载均衡策略

负载均衡策略是调度层的重要组成部分，负责在多个适配应用之间合理分配任务，避免单点过载，提高系统整体性能。我们设计了多种负载均衡策略，适应不同的应用场景。

**1. 负载均衡器设计**

负载均衡器是实现负载均衡策略的核心组件，我们设计了可插拔的负载均衡器架构，支持多种策略的灵活切换。

```java
/**
 * 负载均衡器接口
 */
public interface LoadBalancer {
    
    /**
     * 选择适配器
     * @param task 待执行的任务
     * @param availableAdapters 可用适配器列表
     * @return 选中的适配器ID，如果无法选择则返回null
     */
    String selectAdapter(Task task, List<String> availableAdapters);
    
    /**
     * 获取负载均衡器名称
     * @return 负载均衡器名称
     */
    String getName();
    
    /**
     * 获取负载均衡器描述
     * @return 负载均衡器描述
     */
    String getDescription();
    
    /**
     * 更新适配器状态
     * @param adapterId 适配器ID
     * @param status 适配器状态
     */
    void updateAdapterStatus(String adapterId, AdapterStatus status);
    
    /**
     * 更新适配器负载
     * @param adapterId 适配器ID
     * @param load 负载信息
     */
    void updateAdapterLoad(String adapterId, AdapterLoad load);
}

/**
 * 适配器负载信息
 */
public class AdapterLoad {
    private int activeTaskCount;       // 活动任务数
    private double cpuUsage;           // CPU使用率(0-1)
    private double memoryUsage;        // 内存使用率(0-1)
    private int queueSize;             // 队列大小
    private double responseTimeMs;     // 平均响应时间(毫秒)
    private long timestamp;            // 时间戳
    
    // 构造函数、getters和setters...
}

/**
 * 负载均衡器工厂
 */
public class LoadBalancerFactory {
    private final Map<String, LoadBalancer> loadBalancers = new HashMap<>();
    private final AdapterRegistry adapterRegistry;
    private final Logger logger = LoggerFactory.getLogger(LoadBalancerFactory.class);
    
    public LoadBalancerFactory(AdapterRegistry adapterRegistry) {
        this.adapterRegistry = adapterRegistry;
        
        // 注册内置负载均衡器
        registerLoadBalancer(new RoundRobinLoadBalancer());
        registerLoadBalancer(new WeightedRoundRobinLoadBalancer(adapterRegistry));
        registerLoadBalancer(new LeastConnectionLoadBalancer());
        registerLoadBalancer(new ResponseTimeLoadBalancer());
        registerLoadBalancer(new ResourceAwareLoadBalancer());
    }
    
    /**
     * 注册负载均衡器
     */
    public void registerLoadBalancer(LoadBalancer loadBalancer) {
        loadBalancers.put(loadBalancer.getName(), loadBalancer);
        logger.info("Registered load balancer: {}", loadBalancer.getName());
    }
    
    /**
     * 获取负载均衡器
     */
    public LoadBalancer getLoadBalancer(String name) {
        LoadBalancer loadBalancer = loadBalancers.get(name);
        if (loadBalancer == null) {
            logger.warn("Load balancer not found: {}, using default", name);
            loadBalancer = loadBalancers.get("roundRobin");
        }
        return loadBalancer;
    }
    
    /**
     * 获取所有负载均衡器
     */
    public List<LoadBalancer> getAllLoadBalancers() {
        return new ArrayList<>(loadBalancers.values());
    }
}
```

负载均衡器设计考虑了以下关键点：

- **统一接口**：定义了负载均衡器的标准接口，便于扩展和替换。
- **状态更新**：支持动态更新适配器状态和负载信息，确保决策的准确性。
- **工厂模式**：使用工厂模式创建和管理负载均衡器实例，支持运行时切换策略。
- **可插拔架构**：支持注册自定义负载均衡器，满足特定场景需求。

**2. 轮询策略**

轮询策略是最简单的负载均衡策略，按顺序将任务分配给各适配应用，适用于性能相近的应用。

```java
/**
 * 轮询负载均衡器
 */
public class RoundRobinLoadBalancer implements LoadBalancer {
    private final AtomicInteger counter = new AtomicInteger(0);
    private final Logger logger = LoggerFactory.getLogger(RoundRobinLoadBalancer.class);
    
    @Override
    public String selectAdapter(Task task, List<String> availableAdapters) {
        if (availableAdapters == null || availableAdapters.isEmpty()) {
            return null;
        }
        
        // 如果任务指定了适配器，且该适配器可用，则使用指定适配器
        String preferredAdapter = task.getAdapterId();
        if (preferredAdapter != null && availableAdapters.contains(preferredAdapter)) {
            logger.debug("Using preferred adapter: {}", preferredAdapter);
            return preferredAdapter;
        }
        
        // 轮询选择适配器
        int index = counter.getAndIncrement() % availableAdapters.size();
        String selectedAdapter = availableAdapters.get(index);
        
        logger.debug("Selected adapter: {} (index: {})", selectedAdapter, index);
        return selectedAdapter;
    }
    
    @Override
    public String getName() {
        return "roundRobin";
    }
    
    @Override
    public String getDescription() {
        return "Round Robin Load Balancer";
    }
    
    @Override
    public void updateAdapterStatus(String adapterId, AdapterStatus status) {
        // 轮询策略不需要状态信息
    }
    
    @Override
    public void updateAdapterLoad(String adapterId, AdapterLoad load) {
        // 轮询策略不需要负载信息
    }
}
```

轮询策略的优点是实现简单，不需要额外信息，缺点是不考虑适配应用的实际负载和性能差异，可能导致资源分配不均。

**3. 加权轮询策略**

加权轮询策略根据适配应用的性能和负载能力，分配不同的权重，性能更好的应用获得更多任务。

```java
/**
 * 加权轮询负载均衡器
 */
public class WeightedRoundRobinLoadBalancer implements LoadBalancer {
    private final Map<String, Integer> adapterWeights = new ConcurrentHashMap<>();
    private final AtomicInteger currentIndex = new AtomicInteger(0);
    private final AdapterRegistry adapterRegistry;
    private final Logger logger = LoggerFactory.getLogger(WeightedRoundRobinLoadBalancer.class);
    
    public WeightedRoundRobinLoadBalancer(AdapterRegistry adapterRegistry) {
        this.adapterRegistry = adapterRegistry;
    }
    
    @Override
    public String selectAdapter(Task task, List<String> availableAdapters) {
        if (availableAdapters == null || availableAdapters.isEmpty()) {
            return null;
        }
        
        // 如果任务指定了适配器，且该适配器可用，则使用指定适配器
        String preferredAdapter = task.getAdapterId();
        if (preferredAdapter != null && availableAdapters.contains(preferredAdapter)) {
            logger.debug("Using preferred adapter: {}", preferredAdapter);
            return preferredAdapter;
        }
        
        // 准备权重列表
        List<AdapterWeight> weightList = new ArrayList<>();
        int totalWeight = 0;
        
        for (String adapterId : availableAdapters) {
            int weight = getAdapterWeight(adapterId);
            weightList.add(new AdapterWeight(adapterId, weight));
            totalWeight += weight;
        }
        
        // 如果总权重为0，则使用普通轮询
        if (totalWeight == 0) {
            int index = currentIndex.getAndIncrement() % availableAdapters.size();
            return availableAdapters.get(index);
        }
        
        // 加权选择
        int current = currentIndex.getAndIncrement() % totalWeight;
        int weightSum = 0;
        
        for (AdapterWeight adapterWeight : weightList) {
            weightSum += adapterWeight.getWeight();
            if (current < weightSum) {
                logger.debug("Selected adapter: {} (weight: {})", adapterWeight.getAdapterId(), adapterWeight.getWeight());
                return adapterWeight.getAdapterId();
            }
        }
        
        // 如果没有选中（理论上不会发生），返回第一个适配器
        logger.warn("No adapter selected by weight, using first available");
        return availableAdapters.get(0);
    }
    
    /**
     * 获取适配器权重
     */
    private int getAdapterWeight(String adapterId) {
        // 首先检查是否有预设权重
        Integer weight = adapterWeights.get(adapterId);
        if (weight != null) {
            return weight;
        }
        
        // 否则，从适配器元数据中获取权重
        AdapterMetadata metadata = adapterRegistry.getAdapterMetadata(adapterId);
        if (metadata != null && metadata.getProperties().containsKey("weight")) {
            try {
                weight = Integer.parseInt(metadata.getProperties().get("weight").toString());
                adapterWeights.put(adapterId, weight);
                return weight;
            } catch (NumberFormatException e) {
                logger.warn("Invalid weight value for adapter: {}", adapterId);
            }
        }
        
        // 默认权重为1
        adapterWeights.put(adapterId, 1);
        return 1;
    }
    
    /**
     * 设置适配器权重
     */
    public void setAdapterWeight(String adapterId, int weight) {
        if (weight < 0) {
            throw new IllegalArgumentException("Weight cannot be negative");
        }
        adapterWeights.put(adapterId, weight);
        logger.info("Set weight for adapter {}: {}", adapterId, weight);
    }
    
    @Override
    public String getName() {
        return "weightedRoundRobin";
    }
    
    @Override
    public String getDescription() {
        return "Weighted Round Robin Load Balancer";
    }
    
    @Override
    public void updateAdapterStatus(String adapterId, AdapterStatus status) {
        // 加权轮询策略不直接使用状态信息
    }
    
    @Override
    public void updateAdapterLoad(String adapterId, AdapterLoad load) {
        // 可以根据负载动态调整权重
        // 这里简化实现，实际可能更复杂
        if (load.getCpuUsage() > 0.8) {
            // CPU使用率高，降低权重
            int currentWeight = getAdapterWeight(adapterId);
            int newWeight = Math.max(1, currentWeight - 1);
            setAdapterWeight(adapterId, newWeight);
            logger.debug("Decreased weight for high-load adapter {}: {} -> {}", adapterId, currentWeight, newWeight);
        } else if (load.getCpuUsage() < 0.3) {
            // CPU使用率低，提高权重
            int currentWeight = getAdapterWeight(adapterId);
            int newWeight = currentWeight + 1;
            setAdapterWeight(adapterId, newWeight);
            logger.debug("Increased weight for low-load adapter {}: {} -> {}", adapterId, currentWeight, newWeight);
        }
    }
    
    /**
     * 适配器权重类
     */
    private static class AdapterWeight {
        private final String adapterId;
        private final int weight;
        
        public AdapterWeight(String adapterId, int weight) {
            this.adapterId = adapterId;
            this.weight = weight;
        }
        
        public String getAdapterId() {
            return adapterId;
        }
        
        public int getWeight() {
            return weight;
        }
    }
}
```

加权轮询策略的优点是考虑了适配应用的性能差异，能够更合理地分配任务，缺点是需要手动设置或动态调整权重，增加了配置复杂度。

**4. 最小连接策略**

最小连接策略将任务分配给当前连接数最少的适配应用，避免单点过载，实现动态负载均衡。

```java
/**
 * 最小连接负载均衡器
 */
public class LeastConnectionLoadBalancer implements LoadBalancer {
    private final Map<String, Integer> connectionCounts = new ConcurrentHashMap<>();
    private final Logger logger = LoggerFactory.getLogger(LeastConnectionLoadBalancer.class);
    
    @Override
    public String selectAdapter(Task task, List<String> availableAdapters) {
        if (availableAdapters == null || availableAdapters.isEmpty()) {
            return null;
        }
        
        // 如果任务指定了适配器，且该适配器可用，则使用指定适配器
        String preferredAdapter = task.getAdapterId();
        if (preferredAdapter != null && availableAdapters.contains(preferredAdapter)) {
            logger.debug("Using preferred adapter: {}", preferredAdapter);
            incrementConnectionCount(preferredAdapter);
            return preferredAdapter;
        }
        
        // 找出连接数最少的适配器
        String selectedAdapter = null;
        int minConnections = Integer.MAX_VALUE;
        
        for (String adapterId : availableAdapters) {
            int connections = getConnectionCount(adapterId);
            if (connections < minConnections) {
                minConnections = connections;
                selectedAdapter = adapterId;
            }
        }
        
        if (selectedAdapter != null) {
            logger.debug("Selected adapter: {} (connections: {})", selectedAdapter, minConnections);
            incrementConnectionCount(selectedAdapter);
        }
        
        return selectedAdapter;
    }
    
    /**
     * 获取连接数
     */
    private int getConnectionCount(String adapterId) {
        return connectionCounts.getOrDefault(adapterId, 0);
    }
    
    /**
     * 增加连接数
     */
    private void incrementConnectionCount(String adapterId) {
        connectionCounts.compute(adapterId, (id, count) -> count == null ? 1 : count + 1);
    }
    
    /**
     * 减少连接数
     */
    public void decrementConnectionCount(String adapterId) {
        connectionCounts.compute(adapterId, (id, count) -> count == null || count <= 1 ? 0 : count - 1);
    }
    
    @Override
    public String getName() {
        return "leastConnection";
    }
    
    @Override
    public String getDescription() {
        return "Least Connection Load Balancer";
    }
    
    @Override
    public void updateAdapterStatus(String adapterId, AdapterStatus status) {
        // 如果适配器不可用，重置连接计数
        if (status != AdapterStatus.AVAILABLE) {
            connectionCounts.put(adapterId, 0);
            logger.debug("Reset connection count for unavailable adapter: {}", adapterId);
        }
    }
    
    @Override
    public void updateAdapterLoad(String adapterId, AdapterLoad load) {
        // 更新连接计数
        connectionCounts.put(adapterId, load.getActiveTaskCount());
        logger.debug("Updated connection count for adapter {}: {}", adapterId, load.getActiveTaskCount());
    }
}
```

最小连接策略的优点是能够动态感知适配应用的负载状态，避免过载，缺点是可能导致任务分配不均，新加入的适配应用可能会短时间内接收大量任务。

**5. 响应时间策略**

响应时间策略根据适配应用的历史响应时间，优先选择响应更快的应用，提高系统整体响应速度。

```java
/**
 * 响应时间负载均衡器
 */
public class ResponseTimeLoadBalancer implements LoadBalancer {
    private final Map<String, Double> responseTimesMs = new ConcurrentHashMap<>();
    private final Map<String, Long> lastUpdateTimes = new ConcurrentHashMap<>();
    private final double decayFactor = 0.8; // 衰减因子，控制历史数据的权重
    private final Logger logger = LoggerFactory.getLogger(ResponseTimeLoadBalancer.class);
    
    @Override
    public String selectAdapter(Task task, List<String> availableAdapters) {
        if (availableAdapters == null || availableAdapters.isEmpty()) {
            return null;
        }
        
        // 如果任务指定了适配器，且该适配器可用，则使用指定适配器
        String preferredAdapter = task.getAdapterId();
        if (preferredAdapter != null && availableAdapters.contains(preferredAdapter)) {
            logger.debug("Using preferred adapter: {}", preferredAdapter);
            return preferredAdapter;
        }
        
        // 找出响应时间最短的适配器
        String selectedAdapter = null;
        double minResponseTime = Double.MAX_VALUE;
        
        for (String adapterId : availableAdapters) {
            double responseTime = getResponseTime(adapterId);
            if (responseTime < minResponseTime) {
                minResponseTime = responseTime;
                selectedAdapter = adapterId;
            }
        }
        
        if (selectedAdapter != null) {
            logger.debug("Selected adapter: {} (response time: {}ms)", selectedAdapter, minResponseTime);
        }
        
        return selectedAdapter;
    }
    
    /**
     * 获取响应时间
     */
    private double getResponseTime(String adapterId) {
        Double responseTime = responseTimesMs.get(adapterId);
        if (responseTime == null) {
            // 如果没有历史数据，假设响应时间为0
            return 0;
        }
        
        // 检查数据是否过期（超过5分钟）
        long lastUpdateTime = lastUpdateTimes.getOrDefault(adapterId, 0L);
        if (System.currentTimeMillis() - lastUpdateTime > 300000) {
            // 数据过期，重置为0
            responseTimesMs.remove(adapterId);
            lastUpdateTimes.remove(adapterId);
            return 0;
        }
        
        return responseTime;
    }
    
    /**
     * 更新响应时间
     */
    public void updateResponseTime(String adapterId, double newResponseTimeMs) {
        Double currentResponseTime = responseTimesMs.get(adapterId);
        double updatedResponseTime;
        
        if (currentResponseTime == null) {
            // 首次记录
            updatedResponseTime = newResponseTimeMs;
        } else {
            // 加权平均：新值 = 衰减因子 * 旧值 + (1 - 衰减因子) * 新值
            updatedResponseTime = decayFactor * currentResponseTime + (1 - decayFactor) * newResponseTimeMs;
        }
        
        responseTimesMs.put(adapterId, updatedResponseTime);
        lastUpdateTimes.put(adapterId, System.currentTimeMillis());
        
        logger.debug("Updated response time for adapter {}: {}ms", adapterId, updatedResponseTime);
    }
    
    @Override
    public String getName() {
        return "responseTime";
    }
    
    @Override
    public String getDescription() {
        return "Response Time Load Balancer";
    }
    
    @Override
    public void updateAdapterStatus(String adapterId, AdapterStatus status) {
        // 如果适配器不可用，移除响应时间记录
        if (status != AdapterStatus.AVAILABLE) {
            responseTimesMs.remove(adapterId);
            lastUpdateTimes.remove(adapterId);
            logger.debug("Removed response time data for unavailable adapter: {}", adapterId);
        }
    }
    
    @Override
    public void updateAdapterLoad(String adapterId, AdapterLoad load) {
        // 更新响应时间
        updateResponseTime(adapterId, load.getResponseTimeMs());
    }
}
```

响应时间策略的优点是能够优先选择性能更好的适配应用，提高系统响应速度，缺点是可能导致性能好的适配应用负载过重，而性能差的适配应用闲置。
