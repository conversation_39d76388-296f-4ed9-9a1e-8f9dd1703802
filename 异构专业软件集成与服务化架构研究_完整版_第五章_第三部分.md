# 面向异构专业软件的服务化集成架构研究（续）

#### 5.2.3 组件管理器设计

组件管理器是组件封装层的核心管理组件，负责组件的注册、加载、初始化和生命周期管理，为上层应用提供统一的组件访问接口。

```java
/**
 * 组件管理器接口
 */
public interface ComponentManager {
    
    /**
     * 初始化组件管理器
     * @throws ComponentException 初始化异常
     */
    void initialize() throws ComponentException;
    
    /**
     * 加载组件
     * @throws ComponentException 加载异常
     */
    void loadComponents() throws ComponentException;
    
    /**
     * 获取组件
     * @param componentId 组件ID
     * @return 组件实例
     * @throws ComponentException 获取异常
     */
    Component getComponent(String componentId) throws ComponentException;
    
    /**
     * 获取组件描述符
     * @param componentId 组件ID
     * @return 组件描述符
     * @throws ComponentException 获取异常
     */
    ComponentDescriptor getComponentDescriptor(String componentId) throws ComponentException;
    
    /**
     * 获取所有组件
     * @return 组件ID列表
     */
    List<String> getAllComponentIds();
    
    /**
     * 根据类型获取组件
     * @param type 组件类型
     * @return 组件ID列表
     */
    List<String> getComponentIdsByType(ComponentType type);
    
    /**
     * 根据标签获取组件
     * @param tag 标签
     * @return 组件ID列表
     */
    List<String> getComponentIdsByTag(String tag);
    
    /**
     * 创建组件
     * @param componentId 组件ID
     * @return 组件实例
     * @throws ComponentException 创建异常
     */
    Component createComponent(String componentId) throws ComponentException;
    
    /**
     * 启动组件
     * @param componentId 组件ID
     * @throws ComponentException 启动异常
     */
    void startComponent(String componentId) throws ComponentException;
    
    /**
     * 停止组件
     * @param componentId 组件ID
     * @throws ComponentException 停止异常
     */
    void stopComponent(String componentId) throws ComponentException;
    
    /**
     * 销毁组件
     * @param componentId 组件ID
     * @throws ComponentException 销毁异常
     */
    void destroyComponent(String componentId) throws ComponentException;
    
    /**
     * 获取组件状态
     * @param componentId 组件ID
     * @return 组件状态
     * @throws ComponentException 获取状态异常
     */
    ComponentStatus getComponentStatus(String componentId) throws ComponentException;
    
    /**
     * 执行组件操作
     * @param componentId 组件ID
     * @param operation 操作名称
     * @param params 操作参数
     * @return 操作结果
     * @throws ComponentException 执行异常
     */
    ComponentResult executeOperation(String componentId, String operation, Map<String, Object> params) throws ComponentException;
    
    /**
     * 关闭组件管理器
     * @throws ComponentException 关闭异常
     */
    void shutdown() throws ComponentException;
}

/**
 * 默认组件管理器实现
 */
public class DefaultComponentManager implements ComponentManager {
    private final ComponentRegistry registry;
    private final ComponentLifecycleManager lifecycleManager;
    private final List<ComponentLoader> loaders;
    private final ApplicationEventPublisher eventPublisher;
    private final Logger logger = LoggerFactory.getLogger(DefaultComponentManager.class);
    
    private boolean initialized = false;
    
    public DefaultComponentManager(ComponentRegistry registry, ComponentLifecycleManager lifecycleManager,
                                  List<ComponentLoader> loaders, ApplicationEventPublisher eventPublisher) {
        this.registry = registry;
        this.lifecycleManager = lifecycleManager;
        this.loaders = loaders;
        this.eventPublisher = eventPublisher;
    }
    
    @Override
    public void initialize() throws ComponentException {
        if (initialized) {
            return;
        }
        
        logger.info("Initializing component manager");
        
        try {
            // 加载组件
            loadComponents();
            
            initialized = true;
            logger.info("Component manager initialized");
        } catch (Exception e) {
            logger.error("Failed to initialize component manager", e);
            throw new ComponentException("Failed to initialize component manager", e);
        }
    }
    
    @Override
    public void loadComponents() throws ComponentException {
        logger.info("Loading components");
        
        try {
            int totalLoaded = 0;
            
            // 使用所有加载器加载组件
            for (ComponentLoader loader : loaders) {
                try {
                    logger.debug("Loading components using loader: {}", loader.getName());
                    List<ComponentDescriptor> descriptors = loader.loadComponents();
                    
                    // 注册组件描述符
                    for (ComponentDescriptor descriptor : descriptors) {
                        try {
                            registry.registerComponent(descriptor);
                            totalLoaded++;
                        } catch (ComponentRegistryException e) {
                            logger.error("Failed to register component: {}", descriptor.getId(), e);
                        }
                    }
                } catch (ComponentLoaderException e) {
                    logger.error("Failed to load components using loader: {}", loader.getName(), e);
                }
            }
            
            logger.info("Loaded {} components", totalLoaded);
        } catch (Exception e) {
            logger.error("Failed to load components", e);
            throw new ComponentException("Failed to load components", e);
        }
    }
    
    @Override
    public Component getComponent(String componentId) throws ComponentException {
        logger.debug("Getting component: {}", componentId);
        
        try {
            // 检查组件是否存在
            if (!registry.hasComponent(componentId)) {
                throw new ComponentException("Component not found: " + componentId);
            }
            
            // 获取组件实例，如果不存在则创建
            Component component;
            try {
                component = lifecycleManager.getComponent(componentId);
            } catch (ComponentException e) {
                // 组件不存在，创建新实例
                component = createComponent(componentId);
            }
            
            return component;
        } catch (ComponentException e) {
            logger.error("Failed to get component: {}", componentId, e);
            throw e;
        }
    }
    
    @Override
    public ComponentDescriptor getComponentDescriptor(String componentId) throws ComponentException {
        logger.debug("Getting component descriptor: {}", componentId);
        
        ComponentDescriptor descriptor = registry.getComponentDescriptor(componentId);
        if (descriptor == null) {
            throw new ComponentException("Component descriptor not found: " + componentId);
        }
        
        return descriptor;
    }
    
    @Override
    public List<String> getAllComponentIds() {
        List<ComponentDescriptor> descriptors = registry.getAllComponentDescriptors();
        return descriptors.stream()
                .map(ComponentDescriptor::getId)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<String> getComponentIdsByType(ComponentType type) {
        List<ComponentDescriptor> descriptors = registry.getComponentDescriptorsByType(type);
        return descriptors.stream()
                .map(ComponentDescriptor::getId)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<String> getComponentIdsByTag(String tag) {
        List<ComponentDescriptor> descriptors = registry.getComponentDescriptorsByTag(tag);
        return descriptors.stream()
                .map(ComponentDescriptor::getId)
                .collect(Collectors.toList());
    }
    
    @Override
    public Component createComponent(String componentId) throws ComponentException {
        logger.debug("Creating component: {}", componentId);
        
        try {
            // 检查组件是否存在
            if (!registry.hasComponent(componentId)) {
                throw new ComponentException("Component not found: " + componentId);
            }
            
            // 验证组件依赖
            registry.validateDependencies(componentId);
            
            // 创建组件实例
            Component component = lifecycleManager.createComponent(componentId);
            
            // 发布组件创建事件
            publishComponentEvent(componentId, ComponentEventType.CREATED);
            
            logger.info("Component created: {}", componentId);
            return component;
        } catch (Exception e) {
            logger.error("Failed to create component: {}", componentId, e);
            throw new ComponentException("Failed to create component: " + componentId, e);
        }
    }
    
    @Override
    public void startComponent(String componentId) throws ComponentException {
        logger.debug("Starting component: {}", componentId);
        
        try {
            // 获取组件实例
            Component component = getComponent(componentId);
            
            // 启动组件
            lifecycleManager.startComponent(componentId);
            
            // 发布组件启动事件
            publishComponentEvent(componentId, ComponentEventType.STARTED);
            
            logger.info("Component started: {}", componentId);
        } catch (Exception e) {
            logger.error("Failed to start component: {}", componentId, e);
            throw new ComponentException("Failed to start component: " + componentId, e);
        }
    }
    
    @Override
    public void stopComponent(String componentId) throws ComponentException {
        logger.debug("Stopping component: {}", componentId);
        
        try {
            // 检查组件是否存在
            if (!lifecycleManager.getComponent(componentId)) {
                throw new ComponentException("Component instance not found: " + componentId);
            }
            
            // 停止组件
            lifecycleManager.stopComponent(componentId);
            
            // 发布组件停止事件
            publishComponentEvent(componentId, ComponentEventType.STOPPED);
            
            logger.info("Component stopped: {}", componentId);
        } catch (Exception e) {
            logger.error("Failed to stop component: {}", componentId, e);
            throw new ComponentException("Failed to stop component: " + componentId, e);
        }
    }
    
    @Override
    public void destroyComponent(String componentId) throws ComponentException {
        logger.debug("Destroying component: {}", componentId);
        
        try {
            // 检查组件是否存在
            if (!lifecycleManager.getComponent(componentId)) {
                throw new ComponentException("Component instance not found: " + componentId);
            }
            
            // 销毁组件
            lifecycleManager.destroyComponent(componentId);
            
            // 发布组件销毁事件
            publishComponentEvent(componentId, ComponentEventType.DESTROYED);
            
            logger.info("Component destroyed: {}", componentId);
        } catch (Exception e) {
            logger.error("Failed to destroy component: {}", componentId, e);
            throw new ComponentException("Failed to destroy component: " + componentId, e);
        }
    }
    
    @Override
    public ComponentStatus getComponentStatus(String componentId) throws ComponentException {
        logger.debug("Getting component status: {}", componentId);
        
        try {
            return lifecycleManager.getComponentStatus(componentId);
        } catch (Exception e) {
            logger.error("Failed to get component status: {}", componentId, e);
            throw new ComponentException("Failed to get component status: " + componentId, e);
        }
    }
    
    @Override
    public ComponentResult executeOperation(String componentId, String operation, Map<String, Object> params) throws ComponentException {
        logger.debug("Executing operation: {} on component: {}", operation, componentId);
        
        try {
            // 获取组件实例
            Component component = getComponent(componentId);
            
            // 检查组件状态
            ComponentStatus status = getComponentStatus(componentId);
            if (status != ComponentStatus.RUNNING) {
                throw new ComponentException("Component is not running: " + componentId + ", status: " + status);
            }
            
            // 执行操作
            long startTime = System.currentTimeMillis();
            ComponentResult result = component.execute(operation, params);
            long endTime = System.currentTimeMillis();
            
            // 设置执行时间
            result.setExecutionTime(endTime - startTime);
            
            // 发布操作执行事件
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("operation", operation);
            eventData.put("success", result.isSuccess());
            eventData.put("executionTime", result.getExecutionTime());
            publishComponentEvent(componentId, ComponentEventType.OPERATION_EXECUTED, eventData);
            
            logger.debug("Operation executed: {} on component: {}, success: {}, time: {}ms", 
                    operation, componentId, result.isSuccess(), result.getExecutionTime());
            
            return result;
        } catch (Exception e) {
            logger.error("Failed to execute operation: {} on component: {}", operation, componentId, e);
            throw new ComponentException("Failed to execute operation: " + operation + " on component: " + componentId, e);
        }
    }
    
    @Override
    public void shutdown() throws ComponentException {
        logger.info("Shutting down component manager");
        
        try {
            // 获取所有组件ID
            List<String> componentIds = getAllComponentIds();
            
            // 停止并销毁所有组件
            for (String componentId : componentIds) {
                try {
                    if (lifecycleManager.getComponentStatus(componentId) == ComponentStatus.RUNNING) {
                        stopComponent(componentId);
                    }
                    destroyComponent(componentId);
                } catch (Exception e) {
                    logger.error("Failed to shutdown component: {}", componentId, e);
                }
            }
            
            initialized = false;
            logger.info("Component manager shutdown completed");
        } catch (Exception e) {
            logger.error("Failed to shutdown component manager", e);
            throw new ComponentException("Failed to shutdown component manager", e);
        }
    }
    
    /**
     * 发布组件事件
     * @param componentId 组件ID
     * @param eventType 事件类型
     */
    private void publishComponentEvent(String componentId, ComponentEventType eventType) {
        publishComponentEvent(componentId, eventType, Collections.emptyMap());
    }
    
    /**
     * 发布组件事件
     * @param componentId 组件ID
     * @param eventType 事件类型
     * @param data 事件数据
     */
    private void publishComponentEvent(String componentId, ComponentEventType eventType, Map<String, Object> data) {
        ComponentEvent event = new ComponentEvent(componentId, eventType, data, System.currentTimeMillis());
        eventPublisher.publishEvent(event);
    }
    
    /**
     * 组件事件类型枚举
     */
    public enum ComponentEventType {
        CREATED,
        INITIALIZED,
        STARTED,
        STOPPED,
        DESTROYED,
        OPERATION_EXECUTED,
        ERROR
    }
    
    /**
     * 组件事件类
     */
    public static class ComponentEvent {
        private final String componentId;
        private final ComponentEventType type;
        private final Map<String, Object> data;
        private final long timestamp;
        
        public ComponentEvent(String componentId, ComponentEventType type, Map<String, Object> data, long timestamp) {
            this.componentId = componentId;
            this.type = type;
            this.data = data;
            this.timestamp = timestamp;
        }
        
        // getters...
    }
}
```

组件管理器设计考虑了以下关键点：

- **组件生命周期管理**：管理组件的创建、启动、停止和销毁等生命周期操作，确保资源的正确分配和释放。
- **组件操作执行**：提供统一的组件操作执行接口，支持参数传递和结果返回。
- **组件查询**：支持按ID、类型和标签等多种方式查询组件，便于组件的发现和使用。
- **事件通知**：在组件状态变化和操作执行时发布事件，支持外部系统感知和响应。
- **异常处理**：处理组件操作中的异常，确保系统稳定性。

#### 5.2.4 组件依赖解析

组件依赖解析是组件管理的重要环节，负责解析和注入组件之间的依赖关系，确保组件能够正确协作。

```java
/**
 * 组件依赖解析器
 */
public class ComponentDependencyResolver {
    private final ComponentRegistry registry;
    private final ComponentLifecycleManager lifecycleManager;
    private final Logger logger = LoggerFactory.getLogger(ComponentDependencyResolver.class);
    
    public ComponentDependencyResolver(ComponentRegistry registry, ComponentLifecycleManager lifecycleManager) {
        this.registry = registry;
        this.lifecycleManager = lifecycleManager;
    }
    
    /**
     * 解析组件依赖
     * @param componentId 组件ID
     * @throws ComponentException 解析异常
     */
    public void resolveDependencies(String componentId) throws ComponentException {
        logger.debug("Resolving dependencies for component: {}", componentId);
        
        // 获取组件描述符
        ComponentDescriptor descriptor = registry.getComponentDescriptor(componentId);
        if (descriptor == null) {
            throw new ComponentException("Component descriptor not found: " + componentId);
        }
        
        // 获取组件上下文
        ComponentContext context = lifecycleManager.getComponentContext(componentId);
        if (context == null) {
            throw new ComponentException("Component context not found: " + componentId);
        }
        
        // 解析依赖
        for (DependencyDescriptor dependency : descriptor.getDependencies()) {
            resolveDependency(componentId, dependency, context);
        }
        
        logger.debug("Dependencies resolved for component: {}", componentId);
    }
    
    /**
     * 解析单个依赖
     * @param componentId 组件ID
     * @param dependency 依赖描述符
     * @param context 组件上下文
     * @throws ComponentException 解析异常
     */
    private void resolveDependency(String componentId, DependencyDescriptor dependency, ComponentContext context) throws ComponentException {
        String dependencyType = dependency.getComponentType();
        String dependencyId = dependency.getId();
        boolean required = dependency.isRequired();
        
        logger.debug("Resolving dependency: {} (type: {}, required: {}) for component: {}", 
                dependencyId, dependencyType, required, componentId);
        
        // 查找匹配的组件
        List<ComponentDescriptor> candidates = registry.getComponentDescriptorsByType(ComponentType.valueOf(dependencyType));
        
        // 过滤版本兼容的组件
        List<ComponentDescriptor> compatibleCandidates = candidates.stream()
                .filter(candidate -> isVersionCompatible(candidate.getVersion(), dependency.getMinVersion(), dependency.getMaxVersion()))
                .collect(Collectors.toList());
        
        if (compatibleCandidates.isEmpty()) {
            if (required) {
                throw new ComponentException("Required dependency not found: " + dependencyType + 
                        " for component: " + componentId);
            } else {
                logger.warn("Optional dependency not found: {} for component: {}", dependencyType, componentId);
                return;
            }
        }
        
        // 选择最佳匹配的组件
        ComponentDescriptor selectedCandidate = selectBestCandidate(compatibleCandidates);
        
        // 创建依赖组件实例
        Component dependencyComponent = lifecycleManager.createComponent(selectedCandidate.getId());
        
        // 注入依赖
        context.setDependency(dependencyId, dependencyComponent);
        
        logger.debug("Dependency resolved: {} -> {} for component: {}", 
                dependencyId, selectedCandidate.getId(), componentId);
    }
    
    /**
     * 选择最佳匹配的组件
     * @param candidates 候选组件
     * @return 最佳匹配的组件
     */
    private ComponentDescriptor selectBestCandidate(List<ComponentDescriptor> candidates) {
        // 简化实现，选择第一个候选组件
        // 实际实现可能需要更复杂的选择逻辑，如版本优先级、标签匹配等
        return candidates.get(0);
    }
    
    /**
     * 检查版本兼容性
     * @param version 版本
     * @param minVersion 最小版本
     * @param maxVersion 最大版本
     * @return 是否兼容
     */
    private boolean isVersionCompatible(String version, String minVersion, String maxVersion) {
        // 如果未指定版本要求，则认为兼容
        if ((minVersion == null || minVersion.isEmpty()) && (maxVersion == null || maxVersion.isEmpty())) {
            return true;
        }
        
        // 解析版本
        Version v = parseVersion(version);
        
        // 检查最小版本
        if (minVersion != null && !minVersion.isEmpty()) {
            Version min = parseVersion(minVersion);
            if (v.compareTo(min) < 0) {
                return false;
            }
        }
        
        // 检查最大版本
        if (maxVersion != null && !maxVersion.isEmpty()) {
            Version max = parseVersion(maxVersion);
            if (v.compareTo(max) > 0) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 解析版本字符串
     * @param version 版本字符串
     * @return 版本对象
     */
    private Version parseVersion(String version) {
        // 简化实现，实际可能需要更复杂的版本解析逻辑
        String[] parts = version.split("\\.");
        int major = parts.length > 0 ? Integer.parseInt(parts[0]) : 0;
        int minor = parts.length > 1 ? Integer.parseInt(parts[1]) : 0;
        int patch = parts.length > 2 ? Integer.parseInt(parts[2]) : 0;
        
        return new Version(major, minor, patch);
    }
    
    /**
     * 版本类
     */
    private static class Version implements Comparable<Version> {
        private final int major;
        private final int minor;
        private final int patch;
        
        public Version(int major, int minor, int patch) {
            this.major = major;
            this.minor = minor;
            this.patch = patch;
        }
        
        @Override
        public int compareTo(Version other) {
            if (major != other.major) {
                return Integer.compare(major, other.major);
            }
            if (minor != other.minor) {
                return Integer.compare(minor, other.minor);
            }
            return Integer.compare(patch, other.patch);
        }
    }
}
```

组件依赖解析设计考虑了以下关键点：

- **依赖查找**：根据依赖类型和版本要求查找匹配的组件，支持必需依赖和可选依赖。
- **版本兼容性**：检查组件版本是否满足依赖要求，确保组件兼容性。
- **最佳匹配**：当有多个匹配的组件时，选择最佳匹配的组件，提高依赖解析的准确性。
- **依赖注入**：将解析的依赖组件注入到组件上下文中，支持组件间的协作。
- **错误处理**：处理依赖解析过程中的异常，提供有意义的错误信息。
