# 面向异构专业软件的服务化集成架构研究（续）

**6. 资源感知策略**

资源感知策略是一种更先进的负载均衡策略，它综合考虑系统资源状态和任务特性，实现更精确的任务分配。

```java
/**
 * 资源感知负载均衡器
 */
public class ResourceAwareLoadBalancer implements LoadBalancer {
    private final Map<String, AdapterLoad> adapterLoads = new ConcurrentHashMap<>();
    private final Map<String, AdapterStatus> adapterStatuses = new ConcurrentHashMap<>();
    private final Map<String, Long> lastUpdateTimes = new ConcurrentHashMap<>();
    private final Logger logger = LoggerFactory.getLogger(ResourceAwareLoadBalancer.class);
    
    @Override
    public String selectAdapter(Task task, List<String> availableAdapters) {
        if (availableAdapters == null || availableAdapters.isEmpty()) {
            return null;
        }
        
        // 如果任务指定了适配器，且该适配器可用，则使用指定适配器
        String preferredAdapter = task.getAdapterId();
        if (preferredAdapter != null && availableAdapters.contains(preferredAdapter)) {
            logger.debug("Using preferred adapter: {}", preferredAdapter);
            return preferredAdapter;
        }
        
        // 过滤掉负载过高的适配器
        List<String> candidateAdapters = filterOverloadedAdapters(availableAdapters);
        if (candidateAdapters.isEmpty()) {
            // 如果所有适配器都过载，则使用所有可用适配器
            candidateAdapters = availableAdapters;
        }
        
        // 计算每个适配器的得分
        Map<String, Double> adapterScores = calculateAdapterScores(candidateAdapters, task);
        
        // 选择得分最高的适配器
        String selectedAdapter = null;
        double maxScore = Double.MIN_VALUE;
        
        for (Map.Entry<String, Double> entry : adapterScores.entrySet()) {
            if (entry.getValue() > maxScore) {
                maxScore = entry.getValue();
                selectedAdapter = entry.getKey();
            }
        }
        
        if (selectedAdapter != null) {
            logger.debug("Selected adapter: {} (score: {})", selectedAdapter, maxScore);
        }
        
        return selectedAdapter;
    }
    
    /**
     * 过滤负载过高的适配器
     */
    private List<String> filterOverloadedAdapters(List<String> adapters) {
        List<String> filteredAdapters = new ArrayList<>();
        
        for (String adapterId : adapters) {
            AdapterLoad load = adapterLoads.get(adapterId);
            if (load == null || !isOverloaded(load)) {
                filteredAdapters.add(adapterId);
            }
        }
        
        return filteredAdapters;
    }
    
    /**
     * 判断适配器是否过载
     */
    private boolean isOverloaded(AdapterLoad load) {
        // 如果CPU使用率超过90%或内存使用率超过90%，则认为过载
        return load.getCpuUsage() > 0.9 || load.getMemoryUsage() > 0.9;
    }
    
    /**
     * 计算适配器得分
     */
    private Map<String, Double> calculateAdapterScores(List<String> adapters, Task task) {
        Map<String, Double> scores = new HashMap<>();
        
        for (String adapterId : adapters) {
            AdapterLoad load = adapterLoads.get(adapterId);
            if (load == null) {
                // 如果没有负载信息，给一个默认得分
                scores.put(adapterId, 0.5);
                continue;
            }
            
            // 计算资源得分（CPU和内存）
            double resourceScore = calculateResourceScore(load);
            
            // 计算响应时间得分
            double responseTimeScore = calculateResponseTimeScore(load);
            
            // 计算队列得分
            double queueScore = calculateQueueScore(load);
            
            // 计算任务亲和性得分
            double affinityScore = calculateAffinityScore(adapterId, task);
            
            // 综合得分（权重可以根据实际情况调整）
            double finalScore = resourceScore * 0.4 + responseTimeScore * 0.3 + queueScore * 0.2 + affinityScore * 0.1;
            
            scores.put(adapterId, finalScore);
            logger.debug("Adapter {} score: {} (resource: {}, response: {}, queue: {}, affinity: {})",
                    adapterId, finalScore, resourceScore, responseTimeScore, queueScore, affinityScore);
        }
        
        return scores;
    }
    
    /**
     * 计算资源得分
     */
    private double calculateResourceScore(AdapterLoad load) {
        // 资源得分 = 1 - max(CPU使用率, 内存使用率)
        // 资源使用率越低，得分越高
        return 1 - Math.max(load.getCpuUsage(), load.getMemoryUsage());
    }
    
    /**
     * 计算响应时间得分
     */
    private double calculateResponseTimeScore(AdapterLoad load) {
        // 响应时间得分 = 1 / (1 + 响应时间/1000)
        // 响应时间越短，得分越高
        return 1.0 / (1.0 + load.getResponseTimeMs() / 1000.0);
    }
    
    /**
     * 计算队列得分
     */
    private double calculateQueueScore(AdapterLoad load) {
        // 队列得分 = 1 / (1 + 队列大小/10)
        // 队列越短，得分越高
        return 1.0 / (1.0 + load.getQueueSize() / 10.0);
    }
    
    /**
     * 计算任务亲和性得分
     */
    private double calculateAffinityScore(String adapterId, Task task) {
        // 如果任务之前在此适配器上执行过，则有较高亲和性
        // 简化实现，实际可能更复杂
        return task.getAdapterId() != null && task.getAdapterId().equals(adapterId) ? 1.0 : 0.0;
    }
    
    @Override
    public String getName() {
        return "resourceAware";
    }
    
    @Override
    public String getDescription() {
        return "Resource Aware Load Balancer";
    }
    
    @Override
    public void updateAdapterStatus(String adapterId, AdapterStatus status) {
        adapterStatuses.put(adapterId, status);
        lastUpdateTimes.put(adapterId, System.currentTimeMillis());
        
        // 如果适配器不可用，移除负载信息
        if (status != AdapterStatus.AVAILABLE) {
            adapterLoads.remove(adapterId);
            logger.debug("Removed load data for unavailable adapter: {}", adapterId);
        }
    }
    
    @Override
    public void updateAdapterLoad(String adapterId, AdapterLoad load) {
        adapterLoads.put(adapterId, load);
        lastUpdateTimes.put(adapterId, System.currentTimeMillis());
        logger.debug("Updated load for adapter {}: CPU {}%, Memory {}%, Queue {}, Response {}ms",
                adapterId, load.getCpuUsage() * 100, load.getMemoryUsage() * 100, 
                load.getQueueSize(), load.getResponseTimeMs());
    }
}
```

资源感知策略的优点是能够综合考虑多种因素，实现更精确的负载均衡，缺点是计算复杂度高，需要收集和处理更多的系统状态信息。

**7. 负载均衡策略选择**

不同的负载均衡策略适用于不同的场景，我们需要根据实际情况选择合适的策略：

- **轮询策略**：适用于适配应用性能相近，负载均匀的场景。
- **加权轮询策略**：适用于适配应用性能差异明显，但相对稳定的场景。
- **最小连接策略**：适用于任务执行时间差异大，需要动态平衡负载的场景。
- **响应时间策略**：适用于对响应速度要求高，适配应用性能波动较大的场景。
- **资源感知策略**：适用于系统资源紧张，需要精细控制资源分配的场景。

在实际应用中，可以根据系统特点和业务需求，选择最合适的负载均衡策略，甚至可以实现策略的动态切换，以适应不同的负载条件。

#### 4.1.4 故障恢复机制

故障恢复机制是保障系统可靠性的重要组成部分，负责处理各种异常情况，确保系统能够从故障中恢复并继续运行。我们设计了全面的故障恢复机制，包括任务重试、服务降级、熔断保护和状态恢复等。

**1. 任务重试机制**

任务重试机制负责对因临时故障失败的任务进行重试，提高任务的成功率。我们实现了指数退避重试策略，避免频繁重试对系统造成额外负担。

```java
/**
 * 任务重试管理器
 */
public class TaskRetryManager {
    private final TaskQueueManager queueManager;
    private final TaskRepository taskRepository;
    private final ApplicationEventPublisher eventPublisher;
    private final Logger logger = LoggerFactory.getLogger(TaskRetryManager.class);
    
    public TaskRetryManager(TaskQueueManager queueManager, TaskRepository taskRepository,
                           ApplicationEventPublisher eventPublisher) {
        this.queueManager = queueManager;
        this.taskRepository = taskRepository;
        this.eventPublisher = eventPublisher;
    }
    
    /**
     * 处理任务失败
     */
    public void handleTaskFailure(Task task, String errorMessage) {
        logger.debug("Handling task failure: {}, error: {}", task.getId(), errorMessage);
        
        // 更新任务状态
        task.setStatus(TaskStatus.FAILED);
        task.setErrorMessage(errorMessage);
        task.setEndTime(System.currentTimeMillis());
        
        // 检查是否需要重试
        if (shouldRetry(task)) {
            // 安排重试
            scheduleRetry(task);
        } else {
            // 达到最大重试次数，标记为最终失败
            taskRepository.save(task);
            
            // 发布任务失败事件
            publishTaskEvent(new TaskEvent(this, TaskEventType.FAILED, task));
            
            logger.info("Task failed permanently: {}, max retries reached", task.getId());
        }
    }
    
    /**
     * 判断任务是否应该重试
     */
    private boolean shouldRetry(Task task) {
        // 检查重试次数是否达到上限
        if (task.getRetryCount() >= task.getMaxRetries()) {
            return false;
        }
        
        // 检查错误是否可重试
        if (!isRetryableError(task.getErrorMessage())) {
            return false;
        }
        
        // 检查任务是否已过期
        if (isTaskExpired(task)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 判断错误是否可重试
     */
    private boolean isRetryableError(String errorMessage) {
        if (errorMessage == null) {
            return true;
        }
        
        // 不可重试的错误类型
        List<String> nonRetryableErrors = Arrays.asList(
            "InvalidParameter",
            "AuthorizationFailed",
            "ResourceNotFound",
            "OperationNotSupported"
        );
        
        // 检查错误消息是否包含不可重试的错误类型
        for (String error : nonRetryableErrors) {
            if (errorMessage.contains(error)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 判断任务是否已过期
     */
    private boolean isTaskExpired(Task task) {
        // 如果任务创建时间超过24小时，则认为已过期
        long taskAge = System.currentTimeMillis() - task.getCreateTime();
        return taskAge > 24 * 60 * 60 * 1000;
    }
    
    /**
     * 安排任务重试
     */
    private void scheduleRetry(Task task) {
        logger.debug("Scheduling retry for task: {}, retry count: {}", task.getId(), task.getRetryCount());
        
        // 增加重试计数
        task.setRetryCount(task.getRetryCount() + 1);
        
        // 更新任务状态
        task.setStatus(TaskStatus.RETRY_SCHEDULED);
        taskRepository.save(task);
        
        // 计算重试延迟时间（指数退避策略）
        long retryDelay = calculateRetryDelay(task);
        
        // 提交延迟任务
        queueManager.submitDelayedTask(task, retryDelay, TimeUnit.MILLISECONDS);
        
        // 发布任务重试事件
        publishTaskEvent(new TaskEvent(this, TaskEventType.RETRY_SCHEDULED, task));
        
        logger.info("Task retry scheduled: {}, retry count: {}, delay: {}ms", 
                task.getId(), task.getRetryCount(), retryDelay);
    }
    
    /**
     * 计算重试延迟时间（指数退避策略）
     */
    private long calculateRetryDelay(Task task) {
        // 基础延迟时间
        long baseDelay = task.getRetryDelayMs();
        
        // 如果未设置基础延迟，使用默认值
        if (baseDelay <= 0) {
            baseDelay = 1000; // 1秒
        }
        
        // 指数退避：延迟时间 = 基础延迟 * (2^重试次数) + 随机抖动
        double exponentialFactor = Math.pow(2, task.getRetryCount() - 1);
        long delay = (long) (baseDelay * exponentialFactor);
        
        // 添加随机抖动（0-30%），避免重试风暴
        long jitter = (long) (delay * 0.3 * Math.random());
        delay += jitter;
        
        // 设置最大延迟上限（1小时）
        return Math.min(delay, 3600000);
    }
    
    /**
     * 发布任务事件
     */
    private void publishTaskEvent(TaskEvent event) {
        eventPublisher.publishEvent(event);
    }
}
```

任务重试机制考虑了以下关键点：

- **重试条件**：根据错误类型、重试次数和任务有效期判断是否应该重试。
- **指数退避**：重试间隔随重试次数指数增长，避免频繁重试。
- **随机抖动**：在重试间隔中添加随机抖动，避免重试风暴。
- **最大重试限制**：设置最大重试次数和最大延迟时间，避免无限重试。

**2. 服务降级机制**

服务降级机制负责在某类服务不可用时，启用备选方案或简化功能，确保核心功能可用。

```java
/**
 * 服务降级管理器
 */
public class ServiceDegradationManager {
    private final Map<String, DegradationRule> degradationRules = new ConcurrentHashMap<>();
    private final Map<String, ServiceStatus> serviceStatuses = new ConcurrentHashMap<>();
    private final ApplicationEventPublisher eventPublisher;
    private final Logger logger = LoggerFactory.getLogger(ServiceDegradationManager.class);
    
    public ServiceDegradationManager(ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
    }
    
    /**
     * 注册降级规则
     */
    public void registerDegradationRule(DegradationRule rule) {
        degradationRules.put(rule.getServiceId(), rule);
        logger.info("Registered degradation rule for service: {}", rule.getServiceId());
    }
    
    /**
     * 更新服务状态
     */
    public void updateServiceStatus(String serviceId, ServiceStatus status) {
        ServiceStatus oldStatus = serviceStatuses.put(serviceId, status);
        
        // 如果状态发生变化，检查是否需要降级
        if (oldStatus != status) {
            logger.info("Service status changed: {} -> {}", oldStatus, status);
            checkDegradation(serviceId, status);
        }
    }
    
    /**
     * 检查是否需要降级
     */
    private void checkDegradation(String serviceId, ServiceStatus status) {
        DegradationRule rule = degradationRules.get(serviceId);
        if (rule == null) {
            return;
        }
        
        // 检查是否满足降级条件
        if (status == ServiceStatus.UNAVAILABLE || status == ServiceStatus.DEGRADED) {
            // 执行降级
            performDegradation(rule);
        } else if (status == ServiceStatus.AVAILABLE) {
            // 执行恢复
            performRecovery(rule);
        }
    }
    
    /**
     * 执行降级
     */
    private void performDegradation(DegradationRule rule) {
        logger.info("Performing degradation for service: {}", rule.getServiceId());
        
        // 更新服务状态
        serviceStatuses.put(rule.getServiceId(), ServiceStatus.DEGRADED);
        
        // 执行降级操作
        for (DegradationAction action : rule.getDegradationActions()) {
            try {
                action.execute();
                logger.debug("Executed degradation action: {}", action.getDescription());
            } catch (Exception e) {
                logger.error("Failed to execute degradation action: {}", action.getDescription(), e);
            }
        }
        
        // 发布降级事件
        publishDegradationEvent(rule.getServiceId(), true);
    }
    
    /**
     * 执行恢复
     */
    private void performRecovery(DegradationRule rule) {
        logger.info("Performing recovery for service: {}", rule.getServiceId());
        
        // 更新服务状态
        serviceStatuses.put(rule.getServiceId(), ServiceStatus.AVAILABLE);
        
        // 执行恢复操作
        for (DegradationAction action : rule.getRecoveryActions()) {
            try {
                action.execute();
                logger.debug("Executed recovery action: {}", action.getDescription());
            } catch (Exception e) {
                logger.error("Failed to execute recovery action: {}", action.getDescription(), e);
            }
        }
        
        // 发布恢复事件
        publishDegradationEvent(rule.getServiceId(), false);
    }
    
    /**
     * 发布降级/恢复事件
     */
    private void publishDegradationEvent(String serviceId, boolean degraded) {
        ServiceDegradationEvent event = new ServiceDegradationEvent(this, serviceId, degraded);
        eventPublisher.publishEvent(event);
    }
    
    /**
     * 获取服务状态
     */
    public ServiceStatus getServiceStatus(String serviceId) {
        return serviceStatuses.getOrDefault(serviceId, ServiceStatus.UNKNOWN);
    }
    
    /**
     * 服务状态枚举
     */
    public enum ServiceStatus {
        UNKNOWN,
        AVAILABLE,
        UNAVAILABLE,
        DEGRADED
    }
    
    /**
     * 降级规则类
     */
    public static class DegradationRule {
        private final String serviceId;
        private final List<DegradationAction> degradationActions;
        private final List<DegradationAction> recoveryActions;
        
        public DegradationRule(String serviceId, 
                              List<DegradationAction> degradationActions,
                              List<DegradationAction> recoveryActions) {
            this.serviceId = serviceId;
            this.degradationActions = degradationActions;
            this.recoveryActions = recoveryActions;
        }
        
        // getters...
    }
    
    /**
     * 降级动作接口
     */
    public interface DegradationAction {
        void execute() throws Exception;
        String getDescription();
    }
}
```

服务降级机制考虑了以下关键点：

- **降级规则**：定义服务降级的条件和动作，支持自定义降级策略。
- **状态监控**：实时监控服务状态，及时触发降级或恢复。
- **降级动作**：执行降级操作，如启用备选服务、简化功能等。
- **恢复动作**：服务恢复后执行恢复操作，恢复正常功能。
