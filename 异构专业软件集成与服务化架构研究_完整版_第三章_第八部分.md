# 面向异构专业软件的服务化集成架构研究（续）

#### 3.6.3 企业信息系统集成案例

企业信息系统通常提供Web服务接口，是企业管理和决策的重要支持系统。我们以某企业ERP系统为例，实现了基于Web服务的适配器。

**1. 需求分析**

该ERP系统集成的主要需求包括：
- 查询和管理业务数据（订单、库存、财务等）
- 执行业务流程（采购、销售、生产等）
- 生成业务报表
- 用户认证和权限控制

**2. 接口分析**

该ERP系统提供了基于REST和SOAP的Web服务接口，主要特点包括：
- REST接口采用JSON格式，用于简单的CRUD操作
- SOAP接口采用XML格式，用于复杂的业务流程
- 基于OAuth 2.0的认证机制
- 支持批量操作和事务处理

**3. 适配器实现**

基于前面介绍的统一适配器模式，我们实现了ERP系统适配器：

```java
/**
 * ERP系统适配器
 */
public class ErpSystemAdapter implements SoftwareAdapter {
    private String baseUrl;
    private String accessToken;
    private RestTemplate restTemplate;
    private WebServiceTemplate soapTemplate;
    private boolean connected;
    private final Logger logger = LoggerFactory.getLogger(ErpSystemAdapter.class);
    
    @Override
    public boolean connect(Map<String, Object> connectionParams) throws AdapterException {
        try {
            logger.info("Connecting to ERP system");
            
            // 获取连接参数
            this.baseUrl = (String) connectionParams.get("baseUrl");
            if (baseUrl == null) {
                throw new ConnectionException("Base URL is required");
            }
            
            // 创建REST客户端
            this.restTemplate = new RestTemplate();
            
            // 创建SOAP客户端
            this.soapTemplate = new WebServiceTemplate();
            
            // 配置HTTP客户端
            HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
            requestFactory.setConnectTimeout(5000);
            requestFactory.setReadTimeout(30000);
            restTemplate.setRequestFactory(requestFactory);
            
            // 配置消息转换器
            List<HttpMessageConverter<?>> messageConverters = new ArrayList<>();
            messageConverters.add(new MappingJackson2HttpMessageConverter());
            messageConverters.add(new StringHttpMessageConverter());
            restTemplate.setMessageConverters(messageConverters);
            
            // 执行认证
            String username = (String) connectionParams.get("username");
            String password = (String) connectionParams.get("password");
            authenticate(username, password);
            
            connected = true;
            logger.info("Connected to ERP system successfully");
            return true;
        } catch (Exception e) {
            logger.error("Failed to connect to ERP system", e);
            throw ExceptionConverter.convertWebException(e);
        }
    }
    
    @Override
    public Result executeOperation(String operationName, Map<String, Object> params) throws AdapterException {
        if (!connected) {
            throw new ConnectionException("Not connected to ERP system");
        }
        
        try {
            logger.debug("Executing operation: {} with params: {}", operationName, params);
            
            // 检查访问令牌是否过期
            if (isTokenExpired()) {
                refreshToken();
            }
            
            // 根据操作类型选择不同的执行方式
            if (isRestOperation(operationName)) {
                return executeRestOperation(operationName, params);
            } else {
                return executeSoapOperation(operationName, params);
            }
        } catch (Exception e) {
            logger.error("Failed to execute operation: {}", operationName, e);
            throw ExceptionConverter.convertWebException(e);
        }
    }
    
    @Override
    public void disconnect() throws AdapterException {
        try {
            logger.info("Disconnecting from ERP system");
            
            // 执行登出
            if (connected && accessToken != null) {
                try {
                    HttpHeaders headers = new HttpHeaders();
                    headers.setBearerAuth(accessToken);
                    HttpEntity<String> entity = new HttpEntity<>(headers);
                    restTemplate.exchange(baseUrl + "/api/auth/logout", HttpMethod.POST, entity, String.class);
                } catch (Exception e) {
                    logger.warn("Failed to logout from ERP system", e);
                }
            }
            
            accessToken = null;
            connected = false;
            logger.info("Disconnected from ERP system successfully");
        } catch (Exception e) {
            logger.error("Failed to disconnect from ERP system", e);
            throw ExceptionConverter.convertWebException(e);
        }
    }
    
    // 具体操作实现...
    
    private void authenticate(String username, String password) throws AdapterException {
        try {
            logger.debug("Authenticating with username: {}", username);
            
            // 准备认证请求
            Map<String, String> authRequest = new HashMap<>();
            authRequest.put("username", username);
            authRequest.put("password", password);
            
            // 发送认证请求
            ResponseEntity<Map> response = restTemplate.postForEntity(
                baseUrl + "/api/auth/login",
                authRequest,
                Map.class
            );
            
            // 检查响应
            if (response.getStatusCode().is2xxSuccessful()) {
                Map<String, Object> responseBody = response.getBody();
                if (responseBody != null && responseBody.containsKey("access_token")) {
                    this.accessToken = (String) responseBody.get("access_token");
                    logger.debug("Authentication successful");
                } else {
                    throw new ConnectionException("Invalid authentication response");
                }
            } else {
                throw new ConnectionException("Authentication failed: " + response.getStatusCode());
            }
        } catch (Exception e) {
            logger.error("Authentication failed", e);
            throw ExceptionConverter.convertWebException(e);
        }
    }
    
    private boolean isTokenExpired() {
        // 简化实现，实际应检查令牌的过期时间
        return false;
    }
    
    private void refreshToken() throws AdapterException {
        // 实现令牌刷新逻辑
    }
    
    private boolean isRestOperation(String operationName) {
        // 根据操作名称判断是REST还是SOAP操作
        return operationName.startsWith("rest.");
    }
    
    private Result executeRestOperation(String operationName, Map<String, Object> params) throws AdapterException {
        try {
            // 解析操作名称，格式为"rest.{method}.{resource}"
            String[] parts = operationName.split("\\.");
            if (parts.length < 3) {
                throw new OperationException("Invalid REST operation name: " + operationName);
            }
            
            String method = parts[1].toUpperCase();
            String resource = parts[2];
            
            // 构建URL
            String url = baseUrl + "/api/" + resource;
            
            // 添加查询参数
            if (params.containsKey("queryParams")) {
                @SuppressWarnings("unchecked")
                Map<String, Object> queryParams = (Map<String, Object>) params.get("queryParams");
                UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
                for (Map.Entry<String, Object> entry : queryParams.entrySet()) {
                    builder.queryParam(entry.getKey(), entry.getValue());
                }
                url = builder.build().encode().toUriString();
            }
            
            // 准备请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(accessToken);
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            // 准备请求体
            Object body = params.get("body");
            
            // 创建请求实体
            HttpEntity<?> requestEntity = new HttpEntity<>(body, headers);
            
            // 执行请求
            ResponseEntity<String> response;
            switch (method) {
                case "GET":
                    response = restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class);
                    break;
                case "POST":
                    response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
                    break;
                case "PUT":
                    response = restTemplate.exchange(url, HttpMethod.PUT, requestEntity, String.class);
                    break;
                case "DELETE":
                    response = restTemplate.exchange(url, HttpMethod.DELETE, requestEntity, String.class);
                    break;
                default:
                    throw new OperationException("Unsupported HTTP method: " + method);
            }
            
            // 解析响应
            Map<String, Object> data = new HashMap<>();
            if (response.getBody() != null) {
                ObjectMapper mapper = new ObjectMapper();
                @SuppressWarnings("unchecked")
                Map<String, Object> responseData = mapper.readValue(response.getBody(), Map.class);
                data.put("response", responseData);
            }
            data.put("statusCode", response.getStatusCodeValue());
            data.put("headers", response.getHeaders());
            
            return Result.success(UUID.randomUUID().toString(), data);
        } catch (Exception e) {
            throw ExceptionConverter.convertWebException(e);
        }
    }
    
    private Result executeSoapOperation(String operationName, Map<String, Object> params) throws AdapterException {
        // 实现SOAP操作调用
        // 简化实现，实际需要处理SOAP消息构建和解析
        return Result.failed("SOAP operation not implemented: " + operationName);
    }
}
```

**4. 关键技术点**

在实现ERP系统适配器时，我们解决了以下关键技术问题：

- **认证与授权**：实现OAuth 2.0认证流程，管理访问令牌的获取和刷新。
- **REST/SOAP调用**：统一处理REST和SOAP两种不同类型的Web服务调用。
- **事务管理**：确保跨多个操作的事务一致性。
- **错误处理**：处理各种HTTP错误和业务错误，提供有意义的错误信息。

**5. 性能优化**

为了提高ERP系统适配器的性能，我们采取了以下优化措施：

- **连接池管理**：使用HTTP连接池，复用HTTP连接，减少连接建立的开销。
- **批量操作**：将多个小操作合并为批量操作，减少网络往返次数。
- **压缩传输**：启用HTTP压缩，减少数据传输量。
- **异步处理**：对于耗时操作，采用异步处理方式，提高并发性能。
