# 面向异构专业软件的服务化集成架构研究（续）

### 3.4 数据格式转换

在异构系统集成中，数据格式转换是一个核心挑战。不同系统使用不同的数据表示方式，需要进行有效的转换以保证数据的一致性和完整性。

#### 3.4.1 数据模型设计

为了实现高效的数据转换，我们设计了统一的数据模型，作为不同系统数据格式之间的桥梁。该数据模型具有以下特点：

1. **层次化结构**：支持复杂的嵌套数据结构，可以表示大多数业务数据。
2. **类型系统**：定义了基本类型（字符串、数字、布尔值等）和复合类型（对象、数组等）。
3. **元数据支持**：包含数据类型、格式、约束等元数据信息，便于验证和转换。
4. **扩展性**：支持自定义类型和格式扩展，适应不同领域的特殊需求。

数据模型的核心类如下：

```java
/**
 * 数据值类，表示任意类型的数据
 */
public class DataValue {
    private Object value;
    private DataType type;
    private Map<String, Object> metadata;
    
    // 构造函数、getter和setter方法...
    
    /**
     * 创建字符串值
     */
    public static DataValue ofString(String value) {
        return new DataValue(value, DataType.STRING);
    }
    
    /**
     * 创建数字值
     */
    public static DataValue ofNumber(Number value) {
        return new DataValue(value, DataType.NUMBER);
    }
    
    /**
     * 创建布尔值
     */
    public static DataValue ofBoolean(Boolean value) {
        return new DataValue(value, DataType.BOOLEAN);
    }
    
    /**
     * 创建对象值
     */
    public static DataValue ofObject(Map<String, DataValue> value) {
        return new DataValue(value, DataType.OBJECT);
    }
    
    /**
     * 创建数组值
     */
    public static DataValue ofArray(List<DataValue> value) {
        return new DataValue(value, DataType.ARRAY);
    }
    
    /**
     * 创建二进制值
     */
    public static DataValue ofBinary(byte[] value) {
        return new DataValue(value, DataType.BINARY);
    }
    
    /**
     * 创建日期时间值
     */
    public static DataValue ofDateTime(Date value) {
        return new DataValue(value, DataType.DATETIME);
    }
    
    /**
     * 创建空值
     */
    public static DataValue ofNull() {
        return new DataValue(null, DataType.NULL);
    }
    
    /**
     * 获取字符串值
     */
    public String asString() {
        if (value == null) return null;
        return value.toString();
    }
    
    /**
     * 获取整数值
     */
    public Integer asInteger() {
        if (value == null) return null;
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    // 其他类型转换方法...
}

/**
 * 数据类型枚举
 */
public enum DataType {
    STRING,
    NUMBER,
    BOOLEAN,
    OBJECT,
    ARRAY,
    BINARY,
    DATETIME,
    NULL
}
```

#### 3.4.2 转换策略

基于统一数据模型，我们设计了多层次的数据转换策略：

1. **直接转换**：对于简单类型（字符串、数字、布尔值等），直接进行值转换。
2. **结构转换**：对于复杂结构（对象、数组等），递归转换其内部元素。
3. **类型转换**：在不同类型之间进行智能转换，如字符串到数字、日期到字符串等。
4. **格式转换**：处理不同格式的数据表示，如日期格式、数字格式等。
5. **特殊类型处理**：对二进制数据、大对象等特殊类型进行专门处理。

转换策略的实现代码示例：

```java
/**
 * 数据转换器，负责在不同数据格式之间进行转换
 */
public class DataConverter {
    
    /**
     * 将Java对象转换为统一数据模型
     */
    public static DataValue fromJavaObject(Object obj) {
        if (obj == null) {
            return DataValue.ofNull();
        } else if (obj instanceof String) {
            return DataValue.ofString((String) obj);
        } else if (obj instanceof Number) {
            return DataValue.ofNumber((Number) obj);
        } else if (obj instanceof Boolean) {
            return DataValue.ofBoolean((Boolean) obj);
        } else if (obj instanceof Date) {
            return DataValue.ofDateTime((Date) obj);
        } else if (obj instanceof byte[]) {
            return DataValue.ofBinary((byte[]) obj);
        } else if (obj instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> map = (Map<String, Object>) obj;
            Map<String, DataValue> valueMap = new HashMap<>();
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                valueMap.put(entry.getKey(), fromJavaObject(entry.getValue()));
            }
            return DataValue.ofObject(valueMap);
        } else if (obj instanceof Collection) {
            Collection<?> collection = (Collection<?>) obj;
            List<DataValue> valueList = new ArrayList<>();
            for (Object item : collection) {
                valueList.add(fromJavaObject(item));
            }
            return DataValue.ofArray(valueList);
        } else {
            // 处理其他类型的对象，可以使用反射获取属性
            return DataValue.ofString(obj.toString());
        }
    }
    
    /**
     * 将统一数据模型转换为Java对象
     */
    public static Object toJavaObject(DataValue value) {
        if (value == null || value.getType() == DataType.NULL) {
            return null;
        }
        
        switch (value.getType()) {
            case STRING:
                return value.asString();
            case NUMBER:
                return value.getValue();
            case BOOLEAN:
                return value.asBoolean();
            case DATETIME:
                return value.asDateTime();
            case BINARY:
                return value.asBinary();
            case OBJECT:
                Map<String, Object> map = new HashMap<>();
                Map<String, DataValue> valueMap = value.asObject();
                for (Map.Entry<String, DataValue> entry : valueMap.entrySet()) {
                    map.put(entry.getKey(), toJavaObject(entry.getValue()));
                }
                return map;
            case ARRAY:
                List<Object> list = new ArrayList<>();
                List<DataValue> valueList = value.asArray();
                for (DataValue item : valueList) {
                    list.add(toJavaObject(item));
                }
                return list;
            default:
                return value.getValue();
        }
    }
    
    /**
     * 将统一数据模型转换为JSON
     */
    public static String toJson(DataValue value) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.writeValueAsString(toJavaObject(value));
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to convert to JSON", e);
        }
    }
    
    /**
     * 将JSON转换为统一数据模型
     */
    public static DataValue fromJson(String json) {
        if (json == null || json.isEmpty()) {
            return DataValue.ofNull();
        }
        
        ObjectMapper mapper = new ObjectMapper();
        try {
            Object obj = mapper.readValue(json, Object.class);
            return fromJavaObject(obj);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to parse JSON", e);
        }
    }
    
    /**
     * 将统一数据模型转换为XML
     */
    public static String toXml(DataValue value, String rootName) {
        // XML转换实现...
        return ""; // 简化实现
    }
    
    /**
     * 将XML转换为统一数据模型
     */
    public static DataValue fromXml(String xml) {
        // XML解析实现...
        return DataValue.ofNull(); // 简化实现
    }
    
    // 其他格式转换方法...
}
```

#### 3.4.3 特殊数据处理

在实际集成中，经常需要处理一些特殊类型的数据，如二进制数据、大对象和敏感信息等。我们设计了专门的处理机制：

1. **二进制数据处理**：对于图像、文档等二进制数据，采用Base64编码或文件引用方式传递。

```java
/**
 * 二进制数据处理
 */
public class BinaryDataHandler {
    
    /**
     * 将二进制数据转换为Base64字符串
     */
    public static String toBase64(byte[] data) {
        return Base64.getEncoder().encodeToString(data);
    }
    
    /**
     * 将Base64字符串转换为二进制数据
     */
    public static byte[] fromBase64(String base64) {
        return Base64.getDecoder().decode(base64);
    }
    
    /**
     * 将二进制数据保存为临时文件并返回引用
     */
    public static String saveToTempFile(byte[] data) {
        try {
            Path tempFile = Files.createTempFile("adapter_", ".bin");
            Files.write(tempFile, data);
            return tempFile.toString();
        } catch (IOException e) {
            throw new RuntimeException("Failed to save binary data", e);
        }
    }
    
    /**
     * 从文件加载二进制数据
     */
    public static byte[] loadFromFile(String filePath) {
        try {
            return Files.readAllBytes(Paths.get(filePath));
        } catch (IOException e) {
            throw new RuntimeException("Failed to load binary data", e);
        }
    }
}
```

2. **大对象处理**：对于超过一定大小的数据对象，采用分块传输或流式处理方式。

3. **敏感信息处理**：对于密码、密钥等敏感信息，采用加密存储和传输方式，确保数据安全。

```java
/**
 * 敏感数据处理
 */
public class SensitiveDataHandler {
    
    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/CBC/PKCS5Padding";
    private static final SecretKey SECRET_KEY; // 实际应用中应从安全存储获取
    private static final IvParameterSpec IV_SPEC; // 实际应用中应随机生成并安全传输
    
    static {
        try {
            // 初始化加密密钥和IV（简化实现）
            KeyGenerator keyGen = KeyGenerator.getInstance(ALGORITHM);
            keyGen.init(256);
            SECRET_KEY = keyGen.generateKey();
            
            byte[] iv = new byte[16];
            new SecureRandom().nextBytes(iv);
            IV_SPEC = new IvParameterSpec(iv);
        } catch (Exception e) {
            throw new RuntimeException("Failed to initialize encryption", e);
        }
    }
    
    /**
     * 加密敏感数据
     */
    public static String encrypt(String data) {
        try {
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, SECRET_KEY, IV_SPEC);
            byte[] encrypted = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encrypted);
        } catch (Exception e) {
            throw new RuntimeException("Encryption failed", e);
        }
    }
    
    /**
     * 解密敏感数据
     */
    public static String decrypt(String encryptedData) {
        try {
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, SECRET_KEY, IV_SPEC);
            byte[] original = cipher.doFinal(Base64.getDecoder().decode(encryptedData));
            return new String(original, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("Decryption failed", e);
        }
    }
    
    /**
     * 掩码处理（如信用卡号、身份证号等）
     */
    public static String mask(String data, int prefixLength, int suffixLength) {
        if (data == null || data.length() <= prefixLength + suffixLength) {
            return data;
        }
        
        String prefix = data.substring(0, prefixLength);
        String suffix = data.substring(data.length() - suffixLength);
        String masked = "*".repeat(data.length() - prefixLength - suffixLength);
        
        return prefix + masked + suffix;
    }
}
```

通过这些数据转换机制，我们实现了不同系统间数据的无缝转换，确保了数据的一致性和完整性。
