# 异构专业软件集成与服务化架构研究（续）

### 5.3 组件库管理

#### 5.3.1 组件注册与发现机制

为了方便组件的使用和管理，我们设计了组件注册与发现机制：

1. **组件注册**：组件开发完成后，通过注册接口将组件信息注册到组件库。注册信息包括组件标识、名称、描述、版本、接口定义、依赖关系等。

2. **组件发现**：系统提供多种组件发现方式，包括目录浏览、关键词搜索、标签过滤等，帮助用户快速找到所需组件。

3. **组件订阅**：用户可以订阅关注的组件，当组件更新时自动接收通知。

4. **组件状态管理**：跟踪组件的状态，如开发中、测试中、已发布、已废弃等，帮助用户选择合适的组件。

```java
@RestController
@RequestMapping("/api/components")
public class ComponentRegistryController {
    private final ComponentRegistryService registryService;
    
    @Autowired
    public ComponentRegistryController(ComponentRegistryService registryService) {
        this.registryService = registryService;
    }
    
    // 注册组件
    @PostMapping
    public ResponseEntity<ComponentDTO> registerComponent(@RequestBody ComponentRegistrationRequest request) {
        Component component = registryService.registerComponent(request);
        return ResponseEntity.ok(convertToDTO(component));
    }
    
    // 更新组件
    @PutMapping("/{componentId}")
    public ResponseEntity<ComponentDTO> updateComponent(
            @PathVariable String componentId,
            @RequestBody ComponentUpdateRequest request) {
        Component component = registryService.updateComponent(componentId, request);
        return ResponseEntity.ok(convertToDTO(component));
    }
    
    // 查询组件
    @GetMapping("/{componentId}")
    public ResponseEntity<ComponentDTO> getComponent(@PathVariable String componentId) {
        Component component = registryService.getComponent(componentId);
        return ResponseEntity.ok(convertToDTO(component));
    }
    
    // 搜索组件
    @GetMapping("/search")
    public ResponseEntity<Page<ComponentDTO>> searchComponents(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) ComponentType type,
            @RequestParam(required = false) List<String> tags,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        Page<Component> components = registryService.searchComponents(keyword, type, tags, page, size);
        Page<ComponentDTO> dtos = components.map(this::convertToDTO);
        return ResponseEntity.ok(dtos);
    }
    
    // 变更组件状态
    @PatchMapping("/{componentId}/status")
    public ResponseEntity<ComponentDTO> updateComponentStatus(
            @PathVariable String componentId,
            @RequestBody ComponentStatusUpdateRequest request) {
        Component component = registryService.updateComponentStatus(componentId, request.getStatus());
        return ResponseEntity.ok(convertToDTO(component));
    }
    
    // 转换为DTO
    private ComponentDTO convertToDTO(Component component) {
        // 实现转换逻辑...
        return new ComponentDTO(/* ... */);
    }
}
```

#### 5.3.2 组件元数据管理

组件元数据是描述组件特性和行为的数据，对组件的使用和管理至关重要。我们设计了完善的元数据管理机制：

1. **元数据模型**：定义统一的元数据模型，包括基本信息、接口定义、依赖关系、配置模式等。

2. **元数据验证**：验证元数据的完整性和一致性，确保元数据的质量。

3. **元数据版本控制**：跟踪元数据的变更历史，支持元数据的版本管理。

4. **元数据查询**：提供灵活的元数据查询接口，支持多维度的元数据检索。

```java
@Service
public class ComponentMetadataService {
    private final ComponentRepository componentRepository;
    private final MetadataValidator metadataValidator;
    
    @Autowired
    public ComponentMetadataService(ComponentRepository componentRepository, MetadataValidator metadataValidator) {
        this.componentRepository = componentRepository;
        this.metadataValidator = metadataValidator;
    }
    
    // 保存组件元数据
    @Transactional
    public Component saveMetadata(ComponentMetadata metadata) {
        // 验证元数据
        ValidationResult validationResult = metadataValidator.validate(metadata);
        if (!validationResult.isValid()) {
            throw new InvalidMetadataException("Invalid component metadata: " + validationResult.getErrors());
        }
        
        // 检查是否存在同ID组件
        Optional<Component> existingComponent = componentRepository.findById(metadata.getId());
        
        Component component;
        if (existingComponent.isPresent()) {
            // 更新现有组件
            component = existingComponent.get();
            updateComponentFromMetadata(component, metadata);
        } else {
            // 创建新组件
            component = createComponentFromMetadata(metadata);
        }
        
        // 保存组件
        return componentRepository.save(component);
    }
    
    // 获取组件元数据
    public ComponentMetadata getMetadata(String componentId) {
        Component component = componentRepository.findById(componentId)
                .orElseThrow(() -> new ComponentNotFoundException("Component not found: " + componentId));
        
        return convertToMetadata(component);
    }
    
    // 查询组件元数据
    public List<ComponentMetadata> queryMetadata(MetadataQuery query) {
        // 实现查询逻辑...
        Specification<Component> spec = buildSpecification(query);
        List<Component> components = componentRepository.findAll(spec);
        
        return components.stream()
                .map(this::convertToMetadata)
                .collect(Collectors.toList());
    }
    
    // 从元数据创建组件
    private Component createComponentFromMetadata(ComponentMetadata metadata) {
        Component component = new Component();
        component.setId(metadata.getId());
        updateComponentFromMetadata(component, metadata);
        return component;
    }
    
    // 从元数据更新组件
    private void updateComponentFromMetadata(Component component, ComponentMetadata metadata) {
        component.setName(metadata.getName());
        component.setDescription(metadata.getDescription());
        component.setVersion(metadata.getVersion());
        component.setType(metadata.getType());
        component.setTags(new HashSet<>(metadata.getTags()));
        component.setImplementationClass(metadata.getImplementationClass());
        component.setConfigSchema(metadata.getConfigSchema());
        
        // 更新输入参数
        component.getInputParameters().clear();
        metadata.getInputParameters().forEach(param -> {
            ParameterDefinition paramDef = new ParameterDefinition();
            paramDef.setName(param.getName());
            paramDef.setDescription(param.getDescription());
            paramDef.setType(param.getType());
            paramDef.setRequired(param.isRequired());
            paramDef.setDefaultValue(param.getDefaultValue());
            paramDef.setValidationRules(param.getValidationRules());
            component.getInputParameters().add(paramDef);
        });
        
        // 更新输出参数
        component.getOutputParameters().clear();
        metadata.getOutputParameters().forEach(param -> {
            ParameterDefinition paramDef = new ParameterDefinition();
            paramDef.setName(param.getName());
            paramDef.setDescription(param.getDescription());
            paramDef.setType(param.getType());
            paramDef.setRequired(param.isRequired());
            component.getOutputParameters().add(paramDef);
        });
    }
    
    // 将组件转换为元数据
    private ComponentMetadata convertToMetadata(Component component) {
        ComponentMetadata metadata = new ComponentMetadata();
        metadata.setId(component.getId());
        metadata.setName(component.getName());
        metadata.setDescription(component.getDescription());
        metadata.setVersion(component.getVersion());
        metadata.setType(component.getType());
        metadata.setTags(new ArrayList<>(component.getTags()));
        metadata.setImplementationClass(component.getImplementationClass());
        metadata.setConfigSchema(component.getConfigSchema());
        
        // 转换输入参数
        List<ParameterMetadata> inputParams = component.getInputParameters().stream()
                .map(this::convertToParameterMetadata)
                .collect(Collectors.toList());
        metadata.setInputParameters(inputParams);
        
        // 转换输出参数
        List<ParameterMetadata> outputParams = component.getOutputParameters().stream()
                .map(this::convertToParameterMetadata)
                .collect(Collectors.toList());
        metadata.setOutputParameters(outputParams);
        
        return metadata;
    }
    
    // 将参数定义转换为参数元数据
    private ParameterMetadata convertToParameterMetadata(ParameterDefinition paramDef) {
        ParameterMetadata metadata = new ParameterMetadata();
        metadata.setName(paramDef.getName());
        metadata.setDescription(paramDef.getDescription());
        metadata.setType(paramDef.getType());
        metadata.setRequired(paramDef.isRequired());
        metadata.setDefaultValue(paramDef.getDefaultValue());
        metadata.setValidationRules(paramDef.getValidationRules());
        return metadata;
    }
    
    // 构建查询规范
    private Specification<Component> buildSpecification(MetadataQuery query) {
        // 实现查询规范构建逻辑...
        return (root, criteriaQuery, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            // 按名称查询
            if (query.getName() != null) {
                predicates.add(criteriaBuilder.like(root.get("name"), "%" + query.getName() + "%"));
            }
            
            // 按类型查询
            if (query.getType() != null) {
                predicates.add(criteriaBuilder.equal(root.get("type"), query.getType()));
            }
            
            // 按标签查询
            if (query.getTags() != null && !query.getTags().isEmpty()) {
                predicates.add(root.join("tags").in(query.getTags()));
            }
            
            // 其他查询条件...
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
```
