# 面向异构专业软件的服务化集成架构研究（续）

**3. 熔断保护机制**

熔断保护机制负责监控服务的错误率，当错误率超过阈值时，暂时熔断该服务，避免连锁故障。熔断器模式是一种常用的容错模式，能够有效防止故障扩散。

```java
/**
 * 熔断器管理器
 */
public class CircuitBreakerManager {
    private final Map<String, CircuitBreaker> circuitBreakers = new ConcurrentHashMap<>();
    private final ApplicationEventPublisher eventPublisher;
    private final Logger logger = LoggerFactory.getLogger(CircuitBreakerManager.class);
    
    public CircuitBreakerManager(ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
    }
    
    /**
     * 获取熔断器
     */
    public CircuitBreaker getCircuitBreaker(String serviceId) {
        return circuitBreakers.computeIfAbsent(serviceId, id -> {
            CircuitBreaker breaker = new CircuitBreaker(id, this::publishCircuitBreakerEvent);
            logger.info("Created circuit breaker for service: {}", id);
            return breaker;
        });
    }
    
    /**
     * 记录成功调用
     */
    public void recordSuccess(String serviceId) {
        CircuitBreaker breaker = getCircuitBreaker(serviceId);
        breaker.recordSuccess();
    }
    
    /**
     * 记录失败调用
     */
    public void recordFailure(String serviceId) {
        CircuitBreaker breaker = getCircuitBreaker(serviceId);
        breaker.recordFailure();
    }
    
    /**
     * 检查服务是否可用
     */
    public boolean isServiceAvailable(String serviceId) {
        CircuitBreaker breaker = getCircuitBreaker(serviceId);
        return breaker.isAllowRequest();
    }
    
    /**
     * 重置熔断器
     */
    public void resetCircuitBreaker(String serviceId) {
        CircuitBreaker breaker = getCircuitBreaker(serviceId);
        breaker.reset();
        logger.info("Reset circuit breaker for service: {}", serviceId);
    }
    
    /**
     * 发布熔断器事件
     */
    private void publishCircuitBreakerEvent(String serviceId, CircuitBreakerState oldState, CircuitBreakerState newState) {
        CircuitBreakerEvent event = new CircuitBreakerEvent(this, serviceId, oldState, newState);
        eventPublisher.publishEvent(event);
    }
    
    /**
     * 熔断器类
     */
    public static class CircuitBreaker {
        private final String serviceId;
        private final TriConsumer<String, CircuitBreakerState, CircuitBreakerState> stateChangeListener;
        
        private volatile CircuitBreakerState state = CircuitBreakerState.CLOSED;
        private final AtomicInteger failureCount = new AtomicInteger(0);
        private final AtomicInteger requestCount = new AtomicInteger(0);
        private final AtomicLong lastStateChangeTime = new AtomicLong(System.currentTimeMillis());
        
        // 配置参数
        private final int failureThreshold = 5;        // 失败阈值
        private final double failureRateThreshold = 0.5; // 失败率阈值
        private final int requestVolumeThreshold = 10;  // 请求量阈值
        private final long resetTimeoutMs = 30000;      // 重置超时时间(毫秒)
        private final long halfOpenMaxRequests = 3;     // 半开状态最大请求数
        
        private final Logger logger = LoggerFactory.getLogger(CircuitBreaker.class);
        
        public CircuitBreaker(String serviceId, 
                             TriConsumer<String, CircuitBreakerState, CircuitBreakerState> stateChangeListener) {
            this.serviceId = serviceId;
            this.stateChangeListener = stateChangeListener;
        }
        
        /**
         * 记录成功调用
         */
        public synchronized void recordSuccess() {
            requestCount.incrementAndGet();
            
            if (state == CircuitBreakerState.HALF_OPEN) {
                // 在半开状态下成功，尝试关闭熔断器
                failureCount.set(0);
                requestCount.set(0);
                transitionToState(CircuitBreakerState.CLOSED);
            }
        }
        
        /**
         * 记录失败调用
         */
        public synchronized void recordFailure() {
            requestCount.incrementAndGet();
            failureCount.incrementAndGet();
            
            if (state == CircuitBreakerState.CLOSED) {
                // 检查是否应该打开熔断器
                if (shouldTrip()) {
                    transitionToState(CircuitBreakerState.OPEN);
                }
            } else if (state == CircuitBreakerState.HALF_OPEN) {
                // 在半开状态下失败，重新打开熔断器
                transitionToState(CircuitBreakerState.OPEN);
            }
        }
        
        /**
         * 检查是否应该熔断
         */
        private boolean shouldTrip() {
            // 请求量不足，不触发熔断
            if (requestCount.get() < requestVolumeThreshold) {
                return false;
            }
            
            // 失败次数超过阈值
            if (failureCount.get() >= failureThreshold) {
                return true;
            }
            
            // 失败率超过阈值
            double failureRate = (double) failureCount.get() / requestCount.get();
            return failureRate >= failureRateThreshold;
        }
        
        /**
         * 检查是否允许请求
         */
        public boolean isAllowRequest() {
            if (state == CircuitBreakerState.CLOSED) {
                return true;
            } else if (state == CircuitBreakerState.OPEN) {
                // 检查是否超过重置超时时间
                long elapsedTime = System.currentTimeMillis() - lastStateChangeTime.get();
                if (elapsedTime >= resetTimeoutMs) {
                    // 转换为半开状态
                    synchronized (this) {
                        if (state == CircuitBreakerState.OPEN) {
                            transitionToState(CircuitBreakerState.HALF_OPEN);
                        }
                    }
                    return true;
                }
                return false;
            } else if (state == CircuitBreakerState.HALF_OPEN) {
                // 在半开状态下限制请求数
                return requestCount.get() < halfOpenMaxRequests;
            }
            
            return false;
        }
        
        /**
         * 重置熔断器
         */
        public synchronized void reset() {
            CircuitBreakerState oldState = state;
            state = CircuitBreakerState.CLOSED;
            failureCount.set(0);
            requestCount.set(0);
            lastStateChangeTime.set(System.currentTimeMillis());
            
            if (oldState != CircuitBreakerState.CLOSED) {
                logger.info("Circuit breaker reset: {} -> {}", oldState, state);
                if (stateChangeListener != null) {
                    stateChangeListener.accept(serviceId, oldState, state);
                }
            }
        }
        
        /**
         * 转换状态
         */
        private void transitionToState(CircuitBreakerState newState) {
            CircuitBreakerState oldState = state;
            state = newState;
            lastStateChangeTime.set(System.currentTimeMillis());
            
            logger.info("Circuit breaker state changed: {} -> {}", oldState, newState);
            
            if (stateChangeListener != null) {
                stateChangeListener.accept(serviceId, oldState, newState);
            }
        }
        
        /**
         * 获取当前状态
         */
        public CircuitBreakerState getState() {
            return state;
        }
        
        /**
         * 获取失败计数
         */
        public int getFailureCount() {
            return failureCount.get();
        }
        
        /**
         * 获取请求计数
         */
        public int getRequestCount() {
            return requestCount.get();
        }
    }
    
    /**
     * 熔断器状态枚举
     */
    public enum CircuitBreakerState {
        CLOSED,     // 关闭状态，允许请求通过
        OPEN,       // 打开状态，拒绝所有请求
        HALF_OPEN   // 半开状态，允许有限请求通过，用于探测服务是否恢复
    }
    
    /**
     * 三参数消费者接口
     */
    @FunctionalInterface
    public interface TriConsumer<T, U, V> {
        void accept(T t, U u, V v);
    }
}
```

熔断保护机制考虑了以下关键点：

- **状态管理**：熔断器具有关闭、打开和半开三种状态，根据服务表现动态切换。
- **错误率监控**：统计请求成功率和失败率，当错误率超过阈值时触发熔断。
- **自动恢复**：熔断一段时间后自动进入半开状态，尝试恢复服务。
- **限流保护**：在半开状态下限制请求数量，避免服务过载。

**4. 状态恢复机制**

状态恢复机制负责在服务恢复后，恢复任务执行，确保任务不丢失。我们实现了任务状态持久化和恢复机制，支持系统重启后的任务恢复。

```java
/**
 * 状态恢复管理器
 */
public class StateRecoveryManager {
    private final TaskRepository taskRepository;
    private final TaskQueueManager queueManager;
    private final AdapterRegistry adapterRegistry;
    private final ApplicationEventPublisher eventPublisher;
    private final Logger logger = LoggerFactory.getLogger(StateRecoveryManager.class);
    
    public StateRecoveryManager(TaskRepository taskRepository, TaskQueueManager queueManager,
                               AdapterRegistry adapterRegistry, ApplicationEventPublisher eventPublisher) {
        this.taskRepository = taskRepository;
        this.queueManager = queueManager;
        this.adapterRegistry = adapterRegistry;
        this.eventPublisher = eventPublisher;
    }
    
    /**
     * 系统启动时恢复任务
     */
    public void recoverTasksOnStartup() {
        logger.info("Starting task recovery process");
        
        try {
            // 查找所有未完成的任务
            List<Task> pendingTasks = taskRepository.findByStatusIn(Arrays.asList(
                TaskStatus.CREATED,
                TaskStatus.QUEUED,
                TaskStatus.SCHEDULED,
                TaskStatus.RUNNING,
                TaskStatus.RETRY_SCHEDULED
            ));
            
            logger.info("Found {} pending tasks to recover", pendingTasks.size());
            
            // 按状态分组处理任务
            Map<TaskStatus, List<Task>> tasksByStatus = pendingTasks.stream()
                    .collect(Collectors.groupingBy(Task::getStatus));
            
            // 处理各状态的任务
            recoverCreatedTasks(tasksByStatus.getOrDefault(TaskStatus.CREATED, Collections.emptyList()));
            recoverQueuedTasks(tasksByStatus.getOrDefault(TaskStatus.QUEUED, Collections.emptyList()));
            recoverScheduledTasks(tasksByStatus.getOrDefault(TaskStatus.SCHEDULED, Collections.emptyList()));
            recoverRunningTasks(tasksByStatus.getOrDefault(TaskStatus.RUNNING, Collections.emptyList()));
            recoverRetryScheduledTasks(tasksByStatus.getOrDefault(TaskStatus.RETRY_SCHEDULED, Collections.emptyList()));
            
            logger.info("Task recovery process completed");
        } catch (Exception e) {
            logger.error("Failed to recover tasks", e);
        }
    }
    
    /**
     * 恢复已创建的任务
     */
    private void recoverCreatedTasks(List<Task> tasks) {
        logger.debug("Recovering {} CREATED tasks", tasks.size());
        
        for (Task task : tasks) {
            // 重新提交到队列
            queueManager.submitTask(task);
            logger.debug("Recovered CREATED task: {}", task.getId());
        }
    }
    
    /**
     * 恢复已入队的任务
     */
    private void recoverQueuedTasks(List<Task> tasks) {
        logger.debug("Recovering {} QUEUED tasks", tasks.size());
        
        for (Task task : tasks) {
            // 重新提交到队列
            queueManager.submitTask(task);
            logger.debug("Recovered QUEUED task: {}", task.getId());
        }
    }
    
    /**
     * 恢复已调度的任务
     */
    private void recoverScheduledTasks(List<Task> tasks) {
        logger.debug("Recovering {} SCHEDULED tasks", tasks.size());
        
        for (Task task : tasks) {
            // 检查适配器是否可用
            String adapterId = task.getAdapterId();
            if (adapterId != null && adapterRegistry.isAdapterAvailable(adapterId)) {
                // 适配器可用，重新分配任务
                queueManager.assignTaskToAdapter(task, adapterId);
                logger.debug("Recovered SCHEDULED task: {} to adapter: {}", task.getId(), adapterId);
            } else {
                // 适配器不可用，重新提交到全局队列
                task.setStatus(TaskStatus.QUEUED);
                queueManager.submitTask(task);
                logger.debug("Recovered SCHEDULED task: {} to global queue", task.getId());
            }
        }
    }
    
    /**
     * 恢复正在运行的任务
     */
    private void recoverRunningTasks(List<Task> tasks) {
        logger.debug("Recovering {} RUNNING tasks", tasks.size());
        
        for (Task task : tasks) {
            // 检查任务是否已经完成
            if (isTaskCompleted(task)) {
                // 任务已完成，更新状态
                task.setStatus(TaskStatus.COMPLETED);
                taskRepository.save(task);
                logger.debug("Marked RUNNING task as COMPLETED: {}", task.getId());
            } else {
                // 任务未完成，重新提交
                task.setStatus(TaskStatus.QUEUED);
                queueManager.submitTask(task);
                logger.debug("Recovered RUNNING task: {} to global queue", task.getId());
            }
        }
    }
    
    /**
     * 恢复等待重试的任务
     */
    private void recoverRetryScheduledTasks(List<Task> tasks) {
        logger.debug("Recovering {} RETRY_SCHEDULED tasks", tasks.size());
        
        for (Task task : tasks) {
            // 重新提交到队列，使用原始重试延迟
            long retryDelay = calculateRetryDelay(task);
            queueManager.submitDelayedTask(task, retryDelay, TimeUnit.MILLISECONDS);
            logger.debug("Recovered RETRY_SCHEDULED task: {}, delay: {}ms", task.getId(), retryDelay);
        }
    }
    
    /**
     * 检查任务是否已完成
     */
    private boolean isTaskCompleted(Task task) {
        // 实际实现可能需要查询适配器或检查结果文件
        // 这里简化实现，假设任务未完成
        return false;
    }
    
    /**
     * 计算重试延迟时间
     */
    private long calculateRetryDelay(Task task) {
        // 基础延迟时间
        long baseDelay = task.getRetryDelayMs();
        
        // 如果未设置基础延迟，使用默认值
        if (baseDelay <= 0) {
            baseDelay = 1000; // 1秒
        }
        
        // 指数退避：延迟时间 = 基础延迟 * (2^重试次数)
        return baseDelay * (long) Math.pow(2, task.getRetryCount() - 1);
    }
    
    /**
     * 适配器恢复时恢复任务
     */
    public void recoverTasksOnAdapterRecovery(String adapterId) {
        logger.info("Recovering tasks for adapter: {}", adapterId);
        
        try {
            // 查找分配给该适配器但未完成的任务
            List<Task> pendingTasks = taskRepository.findByAdapterIdAndStatusIn(
                adapterId,
                Arrays.asList(TaskStatus.SCHEDULED, TaskStatus.RUNNING)
            );
            
            logger.info("Found {} pending tasks for adapter: {}", pendingTasks.size(), adapterId);
            
            for (Task task : tasks) {
                // 重新分配任务到适配器
                queueManager.assignTaskToAdapter(task, adapterId);
                logger.debug("Reassigned task: {} to recovered adapter: {}", task.getId(), adapterId);
            }
        } catch (Exception e) {
            logger.error("Failed to recover tasks for adapter: {}", adapterId, e);
        }
    }
    
    /**
     * 发布恢复事件
     */
    private void publishRecoveryEvent(RecoveryEvent event) {
        eventPublisher.publishEvent(event);
    }
    
    /**
     * 恢复事件类
     */
    public static class RecoveryEvent extends ApplicationEvent {
        private final String type;
        private final Object data;
        
        public RecoveryEvent(Object source, String type, Object data) {
            super(source);
            this.type = type;
            this.data = data;
        }
        
        // getters...
    }
}
```

状态恢复机制考虑了以下关键点：

- **任务状态持久化**：将任务状态持久化到数据库，确保系统重启后能够恢复任务。
- **启动时恢复**：系统启动时自动恢复未完成的任务，确保任务不丢失。
- **适配器恢复**：适配器恢复可用时，自动恢复分配给该适配器的任务。
- **状态转换**：根据任务的当前状态，决定恢复策略，如重新提交、重新分配等。

通过这些故障恢复机制，我们实现了系统的高可靠性，即使在各种故障情况下，也能保证任务的正确执行和系统的稳定运行。
