# 面向异构专业软件的服务化集成架构研究（续）

### 4.2 调度层实现技术

调度层的实现涉及多种技术和框架的选择与应用，本节将详细介绍调度层的核心实现技术。

#### 4.2.1 技术栈选择

在实现调度层时，我们综合考虑了功能需求、性能要求和技术成熟度等因素，选择了以下技术栈：

1. **基础框架**：Spring Boot作为基础框架，提供依赖注入、配置管理、Web服务等核心功能。

2. **任务调度**：
   - Quartz用于定时任务调度
   - Spring Task用于简单的定时任务
   - 自定义调度器用于复杂的资源感知调度

3. **消息队列**：
   - RabbitMQ用于任务队列和事件通知
   - Redis用于轻量级消息传递和分布式锁

4. **状态存储**：
   - MySQL用于任务和配置的持久化存储
   - Redis用于缓存和分布式状态管理

5. **监控与管理**：
   - Spring Boot Actuator用于健康检查和指标收集
   - Micrometer用于性能指标监控
   - Prometheus和Grafana用于指标可视化

6. **容错处理**：
   - Resilience4j用于熔断和限流
   - Spring Retry用于重试机制

这些技术的组合使用，为调度层提供了强大的技术支持，满足了异构系统集成的各种需求。

#### 4.2.2 分布式调度实现

为了提高系统的可扩展性和可靠性，我们实现了分布式调度架构，支持多节点部署和负载均衡。

**1. 分布式架构设计**

分布式调度架构包括主节点和工作节点，主节点负责任务分配和协调，工作节点负责任务执行。

```java
/**
 * 分布式调度器
 */
public class DistributedScheduler {
    private final String nodeId;
    private final boolean isMaster;
    private final TaskQueueManager queueManager;
    private final AdapterRegistry adapterRegistry;
    private final TaskExecutor taskExecutor;
    private final RedisTemplate<String, Object> redisTemplate;
    private final ScheduledExecutorService scheduler;
    private final ApplicationEventPublisher eventPublisher;
    private final Logger logger = LoggerFactory.getLogger(DistributedScheduler.class);
    
    private volatile boolean running = false;
    
    public DistributedScheduler(String nodeId, boolean isMaster, 
                               TaskQueueManager queueManager, AdapterRegistry adapterRegistry,
                               TaskExecutor taskExecutor, RedisTemplate<String, Object> redisTemplate,
                               ApplicationEventPublisher eventPublisher) {
        this.nodeId = nodeId;
        this.isMaster = isMaster;
        this.queueManager = queueManager;
        this.adapterRegistry = adapterRegistry;
        this.taskExecutor = taskExecutor;
        this.redisTemplate = redisTemplate;
        this.eventPublisher = eventPublisher;
        this.scheduler = Executors.newScheduledThreadPool(2);
    }
    
    /**
     * 启动调度器
     */
    public void start() {
        if (running) {
            return;
        }
        
        logger.info("Starting distributed scheduler, nodeId: {}, isMaster: {}", nodeId, isMaster);
        running = true;
        
        // 注册节点
        registerNode();
        
        // 启动心跳
        scheduler.scheduleWithFixedDelay(this::sendHeartbeat, 0, 5, TimeUnit.SECONDS);
        
        // 如果是主节点，启动主调度循环
        if (isMaster) {
            scheduler.scheduleWithFixedDelay(this::scheduleTasks, 0, 100, TimeUnit.MILLISECONDS);
        }
        
        // 启动工作节点任务处理
        scheduler.scheduleWithFixedDelay(this::processAssignedTasks, 0, 100, TimeUnit.MILLISECONDS);
        
        logger.info("Distributed scheduler started");
    }
    
    /**
     * 停止调度器
     */
    public void stop() {
        if (!running) {
            return;
        }
        
        logger.info("Stopping distributed scheduler");
        running = false;
        
        // 注销节点
        unregisterNode();
        
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            scheduler.shutdownNow();
        }
        
        logger.info("Distributed scheduler stopped");
    }
    
    /**
     * 注册节点
     */
    private void registerNode() {
        try {
            NodeInfo nodeInfo = new NodeInfo(nodeId, isMaster, System.currentTimeMillis());
            redisTemplate.opsForHash().put("scheduler:nodes", nodeId, nodeInfo);
            logger.info("Registered node: {}", nodeId);
        } catch (Exception e) {
            logger.error("Failed to register node", e);
        }
    }
    
    /**
     * 注销节点
     */
    private void unregisterNode() {
        try {
            redisTemplate.opsForHash().delete("scheduler:nodes", nodeId);
            logger.info("Unregistered node: {}", nodeId);
        } catch (Exception e) {
            logger.error("Failed to unregister node", e);
        }
    }
    
    /**
     * 发送心跳
     */
    private void sendHeartbeat() {
        try {
            NodeInfo nodeInfo = new NodeInfo(nodeId, isMaster, System.currentTimeMillis());
            redisTemplate.opsForHash().put("scheduler:nodes", nodeId, nodeInfo);
            
            // 检查主节点状态
            checkMasterStatus();
        } catch (Exception e) {
            logger.error("Failed to send heartbeat", e);
        }
    }
    
    /**
     * 检查主节点状态
     */
    private void checkMasterStatus() {
        if (isMaster) {
            return; // 已经是主节点，无需检查
        }
        
        try {
            // 获取所有节点信息
            Map<Object, Object> nodesMap = redisTemplate.opsForHash().entries("scheduler:nodes");
            
            // 查找主节点
            boolean masterFound = false;
            long oldestNodeTime = Long.MAX_VALUE;
            String oldestNodeId = null;
            
            for (Map.Entry<Object, Object> entry : nodesMap.entrySet()) {
                String id = (String) entry.getKey();
                NodeInfo info = (NodeInfo) entry.getValue();
                
                // 检查节点是否活跃（最近30秒有心跳）
                boolean isActive = System.currentTimeMillis() - info.getLastHeartbeat() < 30000;
                
                if (isActive) {
                    if (info.isMaster()) {
                        masterFound = true;
                    }
                    
                    // 记录最早注册的节点
                    if (info.getLastHeartbeat() < oldestNodeTime) {
                        oldestNodeTime = info.getLastHeartbeat();
                        oldestNodeId = id;
                    }
                }
            }
            
            // 如果没有主节点，且当前节点是最早注册的，则升级为主节点
            if (!masterFound && nodeId.equals(oldestNodeId)) {
                promoteTpMaster();
            }
        } catch (Exception e) {
            logger.error("Failed to check master status", e);
        }
    }
    
    /**
     * 升级为主节点
     */
    private void promoteTpMaster() {
        logger.info("Promoting node to master: {}", nodeId);
        
        // 更新节点信息
        isMaster = true;
        NodeInfo nodeInfo = new NodeInfo(nodeId, true, System.currentTimeMillis());
        redisTemplate.opsForHash().put("scheduler:nodes", nodeId, nodeInfo);
        
        // 启动主调度循环
        scheduler.scheduleWithFixedDelay(this::scheduleTasks, 0, 100, TimeUnit.MILLISECONDS);
        
        // 发布主节点变更事件
        publishMasterChangeEvent(nodeId);
        
        logger.info("Node promoted to master: {}", nodeId);
    }
    
    /**
     * 主调度循环（仅主节点执行）
     */
    private void scheduleTasks() {
        if (!isMaster || !running) {
            return;
        }
        
        try {
            // 获取可用工作节点
            List<String> availableNodes = getAvailableNodes();
            if (availableNodes.isEmpty()) {
                logger.debug("No available nodes for scheduling");
                return;
            }
            
            // 获取下一个任务
            Task task = queueManager.pollNextTask();
            if (task == null) {
                logger.debug("No tasks in queue for scheduling");
                return;
            }
            
            // 选择工作节点
            String targetNode = selectNode(availableNodes);
            
            // 分配任务到工作节点
            assignTaskToNode(task, targetNode);
        } catch (Exception e) {
            logger.error("Error in task scheduling", e);
        }
    }
    
    /**
     * 获取可用工作节点
     */
    private List<String> getAvailableNodes() {
        List<String> availableNodes = new ArrayList<>();
        
        try {
            // 获取所有节点信息
            Map<Object, Object> nodesMap = redisTemplate.opsForHash().entries("scheduler:nodes");
            
            for (Map.Entry<Object, Object> entry : nodesMap.entrySet()) {
                String id = (String) entry.getKey();
                NodeInfo info = (NodeInfo) entry.getValue();
                
                // 检查节点是否活跃（最近30秒有心跳）
                boolean isActive = System.currentTimeMillis() - info.getLastHeartbeat() < 30000;
                
                if (isActive) {
                    availableNodes.add(id);
                }
            }
        } catch (Exception e) {
            logger.error("Failed to get available nodes", e);
        }
        
        return availableNodes;
    }
    
    /**
     * 选择工作节点
     */
    private String selectNode(List<String> availableNodes) {
        // 简单的轮询策略
        // 实际实现可能更复杂，考虑负载、资源等因素
        int index = (int) (System.currentTimeMillis() % availableNodes.size());
        return availableNodes.get(index);
    }
    
    /**
     * 分配任务到工作节点
     */
    private void assignTaskToNode(Task task, String nodeId) {
        logger.debug("Assigning task {} to node {}", task.getId(), nodeId);
        
        try {
            // 更新任务状态
            task.setStatus(TaskStatus.SCHEDULED);
            
            // 将任务放入节点队列
            redisTemplate.opsForList().rightPush("scheduler:node:" + nodeId + ":tasks", task);
            
            logger.info("Task {} assigned to node {}", task.getId(), nodeId);
        } catch (Exception e) {
            logger.error("Failed to assign task to node", e);
            
            // 分配失败，将任务重新放回队列
            queueManager.submitTask(task);
        }
    }
    
    /**
     * 处理分配给当前节点的任务
     */
    private void processAssignedTasks() {
        if (!running) {
            return;
        }
        
        try {
            // 从节点队列获取任务
            Task task = (Task) redisTemplate.opsForList().leftPop("scheduler:node:" + nodeId + ":tasks");
            if (task == null) {
                return;
            }
            
            logger.debug("Processing assigned task: {}", task.getId());
            
            // 执行任务
            executeTask(task);
        } catch (Exception e) {
            logger.error("Error in processing assigned tasks", e);
        }
    }
    
    /**
     * 执行任务
     */
    private void executeTask(Task task) {
        logger.debug("Executing task: {}", task.getId());
        
        // 更新任务状态
        task.setStatus(TaskStatus.RUNNING);
        task.setStartTime(System.currentTimeMillis());
        
        // 发布任务开始事件
        publishTaskEvent(new TaskEvent(this, TaskEventType.STARTED, task));
        
        // 异步执行任务
        CompletableFuture.runAsync(() -> {
            try {
                // 执行任务
                Result result = taskExecutor.executeTask(task);
                
                // 处理执行结果
                handleTaskResult(task, result);
            } catch (Exception e) {
                // 处理执行异常
                handleTaskException(task, e);
            }
        });
    }
    
    /**
     * 处理任务执行结果
     */
    private void handleTaskResult(Task task, Result result) {
        logger.debug("Handling task result: {}, success: {}", task.getId(), result.isSuccess());
        
        // 更新任务状态和结果
        task.setEndTime(System.currentTimeMillis());
        task.setResult(result);
        
        if (result.isSuccess()) {
            // 任务成功
            task.setStatus(TaskStatus.COMPLETED);
            
            // 发布任务完成事件
            publishTaskEvent(new TaskEvent(this, TaskEventType.COMPLETED, task));
        } else {
            // 任务失败
            task.setStatus(TaskStatus.FAILED);
            task.setErrorMessage(result.getMessage());
            
            // 检查是否需要重试
            if (task.getRetryCount() < task.getMaxRetries()) {
                // 安排重试
                scheduleRetry(task);
            } else {
                // 发布任务失败事件
                publishTaskEvent(new TaskEvent(this, TaskEventType.FAILED, task));
            }
        }
    }
    
    /**
     * 处理任务执行异常
     */
    private void handleTaskException(Task task, Exception e) {
        logger.error("Task execution failed: {}", task.getId(), e);
        
        // 更新任务状态
        task.setEndTime(System.currentTimeMillis());
        task.setStatus(TaskStatus.FAILED);
        task.setErrorMessage(e.getMessage());
        
        // 检查是否需要重试
        if (task.getRetryCount() < task.getMaxRetries()) {
            // 安排重试
            scheduleRetry(task);
        } else {
            // 发布任务失败事件
            publishTaskEvent(new TaskEvent(this, TaskEventType.FAILED, task));
        }
    }
    
    /**
     * 安排任务重试
     */
    private void scheduleRetry(Task task) {
        logger.debug("Scheduling retry for task: {}, retry count: {}", task.getId(), task.getRetryCount());
        
        // 增加重试计数
        task.setRetryCount(task.getRetryCount() + 1);
        
        // 更新任务状态
        task.setStatus(TaskStatus.RETRY_SCHEDULED);
        
        // 计算重试延迟时间
        long retryDelay = calculateRetryDelay(task);
        
        // 提交延迟任务
        queueManager.submitDelayedTask(task, retryDelay, TimeUnit.MILLISECONDS);
        
        // 发布任务重试事件
        publishTaskEvent(new TaskEvent(this, TaskEventType.RETRY_SCHEDULED, task));
    }
    
    /**
     * 计算重试延迟时间
     */
    private long calculateRetryDelay(Task task) {
        // 基础延迟时间
        long baseDelay = task.getRetryDelayMs();
        
        // 如果未设置基础延迟，使用默认值
        if (baseDelay <= 0) {
            baseDelay = 1000; // 1秒
        }
        
        // 指数退避：延迟时间 = 基础延迟 * (2^重试次数)
        return baseDelay * (long) Math.pow(2, task.getRetryCount() - 1);
    }
    
    /**
     * 发布任务事件
     */
    private void publishTaskEvent(TaskEvent event) {
        eventPublisher.publishEvent(event);
    }
    
    /**
     * 发布主节点变更事件
     */
    private void publishMasterChangeEvent(String newMasterNodeId) {
        MasterChangeEvent event = new MasterChangeEvent(this, newMasterNodeId);
        eventPublisher.publishEvent(event);
    }
    
    /**
     * 节点信息类
     */
    public static class NodeInfo implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private final String nodeId;
        private final boolean master;
        private final long lastHeartbeat;
        
        public NodeInfo(String nodeId, boolean master, long lastHeartbeat) {
            this.nodeId = nodeId;
            this.master = master;
            this.lastHeartbeat = lastHeartbeat;
        }
        
        // getters...
    }
    
    /**
     * 主节点变更事件
     */
    public static class MasterChangeEvent extends ApplicationEvent {
        private final String newMasterNodeId;
        
        public MasterChangeEvent(Object source, String newMasterNodeId) {
            super(source);
            this.newMasterNodeId = newMasterNodeId;
        }
        
        public String getNewMasterNodeId() {
            return newMasterNodeId;
        }
    }
}
```

分布式调度架构考虑了以下关键点：

- **节点角色**：区分主节点和工作节点，主节点负责任务分配，工作节点负责任务执行。
- **心跳机制**：通过定期心跳检测节点状态，及时发现节点故障。
- **主节点选举**：当主节点故障时，自动选举新的主节点，确保系统持续运行。
- **任务分配**：主节点根据负载均衡策略，将任务分配给工作节点。
- **状态同步**：通过分布式存储（Redis）同步节点状态和任务信息。

**2. 分布式锁实现**

在分布式环境中，多个节点可能同时访问共享资源，需要使用分布式锁确保数据一致性。我们基于Redis实现了分布式锁机制。

```java
/**
 * Redis分布式锁
 */
public class RedisDistributedLock {
    private final StringRedisTemplate redisTemplate;
    private final String lockKey;
    private final String lockValue;
    private final long expireTimeMs;
    private final Logger logger = LoggerFactory.getLogger(RedisDistributedLock.class);
    
    public RedisDistributedLock(StringRedisTemplate redisTemplate, String lockKey, long expireTimeMs) {
        this.redisTemplate = redisTemplate;
        this.lockKey = "lock:" + lockKey;
        this.lockValue = UUID.randomUUID().toString();
        this.expireTimeMs = expireTimeMs;
    }
    
    /**
     * 尝试获取锁
     */
    public boolean tryLock() {
        try {
            Boolean result = redisTemplate.opsForValue().setIfAbsent(lockKey, lockValue, expireTimeMs, TimeUnit.MILLISECONDS);
            boolean locked = Boolean.TRUE.equals(result);
            
            if (locked) {
                logger.debug("Acquired lock: {}", lockKey);
            } else {
                logger.debug("Failed to acquire lock: {}", lockKey);
            }
            
            return locked;
        } catch (Exception e) {
            logger.error("Error acquiring lock: {}", lockKey, e);
            return false;
        }
    }
    
    /**
     * 尝试获取锁，带超时
     */
    public boolean tryLock(long waitTimeMs) {
        long startTime = System.currentTimeMillis();
        long waitTime = waitTimeMs;
        
        do {
            if (tryLock()) {
                return true;
            }
            
            // 等待一段时间再重试
            try {
                Thread.sleep(Math.min(100, waitTime));
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
            
            waitTime = waitTimeMs - (System.currentTimeMillis() - startTime);
        } while (waitTime > 0);
        
        return false;
    }
    
    /**
     * 释放锁
     */
    public boolean unlock() {
        try {
            // 使用Lua脚本确保原子性操作
            String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
            Long result = redisTemplate.execute(new DefaultRedisScript<>(script, Long.class), 
                    Collections.singletonList(lockKey), lockValue);
            
            boolean unlocked = result != null && result == 1;
            
            if (unlocked) {
                logger.debug("Released lock: {}", lockKey);
            } else {
                logger.warn("Failed to release lock: {}, it might have expired or been acquired by another thread", lockKey);
            }
            
            return unlocked;
        } catch (Exception e) {
            logger.error("Error releasing lock: {}", lockKey, e);
            return false;
        }
    }
}
```

分布式锁实现考虑了以下关键点：

- **原子操作**：使用Redis的SETNX命令和Lua脚本确保锁操作的原子性。
- **锁超时**：设置锁的过期时间，避免死锁。
- **锁标识**：使用唯一标识作为锁值，确保锁只能被持有者释放。
- **重试机制**：支持带超时的锁获取，在指定时间内多次尝试获取锁。
