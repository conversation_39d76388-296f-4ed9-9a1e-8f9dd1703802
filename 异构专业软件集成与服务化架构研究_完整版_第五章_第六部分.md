# 面向异构专业软件的服务化集成架构研究（续）

### 5.4 组件封装实例

为了验证组件封装层的设计和实现，我们选择了几个典型的异构专业软件，实现了相应的组件封装。本节将详细介绍这些组件封装实例。

#### 5.4.1 CAD软件组件封装

CAD软件是工程设计领域的重要工具，我们以AutoCAD为例，实现了CAD软件的组件封装，提供了图形创建、编辑和查询等功能。

```java
/**
 * AutoCAD组件
 */
public class AutoCadComponent extends AbstractAdapterComponent {
    private static final String COMPONENT_ID = "autoCadComponent";
    private static final String COMPONENT_NAME = "AutoCAD Component";
    private static final String COMPONENT_VERSION = "1.0.0";
    
    @Override
    protected SoftwareAdapter createAdapter() throws ComponentException {
        logger.debug("Creating AutoCAD adapter");
        
        try {
            // 获取适配器类型
            String adapterType = context.getConfig().getProperties().getOrDefault("adapterType", "COM").toString();
            
            // 创建适配器
            SoftwareAdapter adapter;
            if ("COM".equals(adapterType)) {
                adapter = new AutoCadComAdapter();
            } else if ("NET".equals(adapterType)) {
                adapter = new AutoCadNetAdapter();
            } else {
                throw new ComponentException("Unsupported adapter type: " + adapterType);
            }
            
            logger.info("Created AutoCAD adapter: {}", adapterType);
            return adapter;
        } catch (Exception e) {
            logger.error("Failed to create AutoCAD adapter", e);
            throw new ComponentException("Failed to create AutoCAD adapter", e);
        }
    }
    
    @Override
    public List<OperationInfo> getSupportedOperations() {
        List<OperationInfo> operations = new ArrayList<>();
        
        // 创建图形操作
        OperationInfo createLine = new OperationInfo();
        createLine.setName("createLine");
        createLine.setDescription("Create a line");
        createLine.setParameters(Arrays.asList(
            new ParameterInfo("startX", "Start X coordinate", "double", true, null, null),
            new ParameterInfo("startY", "Start Y coordinate", "double", true, null, null),
            new ParameterInfo("startZ", "Start Z coordinate", "double", false, 0.0, null),
            new ParameterInfo("endX", "End X coordinate", "double", true, null, null),
            new ParameterInfo("endY", "End Y coordinate", "double", true, null, null),
            new ParameterInfo("endZ", "End Z coordinate", "double", false, 0.0, null),
            new ParameterInfo("layer", "Layer name", "string", false, "0", null)
        ));
        createLine.setReturnType(new ParameterInfo("lineId", "Line ID", "string", true, null, null));
        operations.add(createLine);
        
        // 创建圆操作
        OperationInfo createCircle = new OperationInfo();
        createCircle.setName("createCircle");
        createCircle.setDescription("Create a circle");
        createCircle.setParameters(Arrays.asList(
            new ParameterInfo("centerX", "Center X coordinate", "double", true, null, null),
            new ParameterInfo("centerY", "Center Y coordinate", "double", true, null, null),
            new ParameterInfo("centerZ", "Center Z coordinate", "double", false, 0.0, null),
            new ParameterInfo("radius", "Radius", "double", true, null, null),
            new ParameterInfo("layer", "Layer name", "string", false, "0", null)
        ));
        createCircle.setReturnType(new ParameterInfo("circleId", "Circle ID", "string", true, null, null));
        operations.add(createCircle);
        
        // 删除图形操作
        OperationInfo deleteEntity = new OperationInfo();
        deleteEntity.setName("deleteEntity");
        deleteEntity.setDescription("Delete an entity");
        deleteEntity.setParameters(Arrays.asList(
            new ParameterInfo("entityId", "Entity ID", "string", true, null, null)
        ));
        deleteEntity.setReturnType(new ParameterInfo("success", "Success", "boolean", true, null, null));
        operations.add(deleteEntity);
        
        // 获取图形属性操作
        OperationInfo getEntityProperties = new OperationInfo();
        getEntityProperties.setName("getEntityProperties");
        getEntityProperties.setDescription("Get entity properties");
        getEntityProperties.setParameters(Arrays.asList(
            new ParameterInfo("entityId", "Entity ID", "string", true, null, null)
        ));
        getEntityProperties.setReturnType(new ParameterInfo("properties", "Entity properties", "map", true, null, null));
        operations.add(getEntityProperties);
        
        // 设置图形属性操作
        OperationInfo setEntityProperties = new OperationInfo();
        setEntityProperties.setName("setEntityProperties");
        setEntityProperties.setDescription("Set entity properties");
        setEntityProperties.setParameters(Arrays.asList(
            new ParameterInfo("entityId", "Entity ID", "string", true, null, null),
            new ParameterInfo("properties", "Entity properties", "map", true, null, null)
        ));
        setEntityProperties.setReturnType(new ParameterInfo("success", "Success", "boolean", true, null, null));
        operations.add(setEntityProperties);
        
        // 保存图形操作
        OperationInfo saveDrawing = new OperationInfo();
        saveDrawing.setName("saveDrawing");
        saveDrawing.setDescription("Save drawing");
        saveDrawing.setParameters(Arrays.asList(
            new ParameterInfo("filePath", "File path", "string", true, null, null),
            new ParameterInfo("overwrite", "Overwrite if exists", "boolean", false, false, null)
        ));
        saveDrawing.setReturnType(new ParameterInfo("success", "Success", "boolean", true, null, null));
        operations.add(saveDrawing);
        
        // 打开图形操作
        OperationInfo openDrawing = new OperationInfo();
        openDrawing.setName("openDrawing");
        openDrawing.setDescription("Open drawing");
        openDrawing.setParameters(Arrays.asList(
            new ParameterInfo("filePath", "File path", "string", true, null, null)
        ));
        openDrawing.setReturnType(new ParameterInfo("success", "Success", "boolean", true, null, null));
        operations.add(openDrawing);
        
        return operations;
    }
}

/**
 * AutoCAD COM适配器
 */
public class AutoCadComAdapter implements SoftwareAdapter {
    private final Logger logger = LoggerFactory.getLogger(AutoCadComAdapter.class);
    private ComObject application;
    private ComObject document;
    private boolean connected = false;
    
    @Override
    public boolean connect(Map<String, Object> params) throws AdapterException {
        logger.debug("Connecting to AutoCAD via COM");
        
        try {
            // 获取AutoCAD应用程序
            application = ComObjectFactory.createObject("AutoCAD.Application");
            
            // 设置可见性
            boolean visible = Boolean.parseBoolean(params.getOrDefault("visible", "true").toString());
            application.setProperty("Visible", visible);
            
            // 获取当前文档
            document = application.getProperty("ActiveDocument");
            if (document == null) {
                // 创建新文档
                document = application.invoke("Documents").invoke("Add");
            }
            
            connected = true;
            logger.info("Connected to AutoCAD via COM");
            return true;
        } catch (Exception e) {
            logger.error("Failed to connect to AutoCAD via COM", e);
            throw new AdapterException("Failed to connect to AutoCAD via COM", e);
        }
    }
    
    @Override
    public Result executeOperation(String operationName, Map<String, Object> params) throws AdapterException {
        logger.debug("Executing AutoCAD operation: {}", operationName);
        
        if (!connected) {
            throw new AdapterException("Not connected to AutoCAD");
        }
        
        try {
            switch (operationName) {
                case "createLine":
                    return createLine(params);
                case "createCircle":
                    return createCircle(params);
                case "deleteEntity":
                    return deleteEntity(params);
                case "getEntityProperties":
                    return getEntityProperties(params);
                case "setEntityProperties":
                    return setEntityProperties(params);
                case "saveDrawing":
                    return saveDrawing(params);
                case "openDrawing":
                    return openDrawing(params);
                default:
                    throw new AdapterException("Unsupported operation: " + operationName);
            }
        } catch (Exception e) {
            logger.error("Failed to execute AutoCAD operation: {}", operationName, e);
            throw new AdapterException("Failed to execute AutoCAD operation: " + operationName, e);
        }
    }
    
    @Override
    public void disconnect() throws AdapterException {
        logger.debug("Disconnecting from AutoCAD");
        
        try {
            if (application != null) {
                // 关闭AutoCAD
                application.invoke("Quit");
                application = null;
                document = null;
            }
            
            connected = false;
            logger.info("Disconnected from AutoCAD");
        } catch (Exception e) {
            logger.error("Failed to disconnect from AutoCAD", e);
            throw new AdapterException("Failed to disconnect from AutoCAD", e);
        }
    }
    
    /**
     * 创建线段
     */
    private Result createLine(Map<String, Object> params) throws AdapterException {
        try {
            // 获取参数
            double startX = getDoubleParam(params, "startX");
            double startY = getDoubleParam(params, "startY");
            double startZ = getDoubleParam(params, "startZ", 0.0);
            double endX = getDoubleParam(params, "endX");
            double endY = getDoubleParam(params, "endY");
            double endZ = getDoubleParam(params, "endZ", 0.0);
            String layer = getStringParam(params, "layer", "0");
            
            // 获取模型空间
            ComObject modelSpace = document.getProperty("ModelSpace");
            
            // 创建起点和终点
            ComObject startPoint = createPoint(startX, startY, startZ);
            ComObject endPoint = createPoint(endX, endY, endZ);
            
            // 创建线段
            ComObject line = modelSpace.invoke("AddLine", startPoint, endPoint);
            
            // 设置图层
            line.setProperty("Layer", layer);
            
            // 获取线段ID
            String lineId = line.getProperty("Handle").toString();
            
            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("lineId", lineId);
            
            return Result.success(lineId, result);
        } catch (Exception e) {
            throw new AdapterException("Failed to create line", e);
        }
    }
    
    /**
     * 创建圆
     */
    private Result createCircle(Map<String, Object> params) throws AdapterException {
        try {
            // 获取参数
            double centerX = getDoubleParam(params, "centerX");
            double centerY = getDoubleParam(params, "centerY");
            double centerZ = getDoubleParam(params, "centerZ", 0.0);
            double radius = getDoubleParam(params, "radius");
            String layer = getStringParam(params, "layer", "0");
            
            // 获取模型空间
            ComObject modelSpace = document.getProperty("ModelSpace");
            
            // 创建中心点
            ComObject centerPoint = createPoint(centerX, centerY, centerZ);
            
            // 创建圆
            ComObject circle = modelSpace.invoke("AddCircle", centerPoint, radius);
            
            // 设置图层
            circle.setProperty("Layer", layer);
            
            // 获取圆ID
            String circleId = circle.getProperty("Handle").toString();
            
            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("circleId", circleId);
            
            return Result.success(circleId, result);
        } catch (Exception e) {
            throw new AdapterException("Failed to create circle", e);
        }
    }
    
    /**
     * 删除图形
     */
    private Result deleteEntity(Map<String, Object> params) throws AdapterException {
        try {
            // 获取参数
            String entityId = getStringParam(params, "entityId");
            
            // 获取图形
            ComObject entity = getEntityById(entityId);
            if (entity == null) {
                throw new AdapterException("Entity not found: " + entityId);
            }
            
            // 删除图形
            entity.invoke("Delete");
            
            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            
            return Result.success(entityId, result);
        } catch (Exception e) {
            throw new AdapterException("Failed to delete entity", e);
        }
    }
    
    /**
     * 获取图形属性
     */
    private Result getEntityProperties(Map<String, Object> params) throws AdapterException {
        try {
            // 获取参数
            String entityId = getStringParam(params, "entityId");
            
            // 获取图形
            ComObject entity = getEntityById(entityId);
            if (entity == null) {
                throw new AdapterException("Entity not found: " + entityId);
            }
            
            // 获取图形类型
            String entityType = entity.getProperty("EntityType").toString();
            
            // 获取通用属性
            Map<String, Object> properties = new HashMap<>();
            properties.put("entityId", entityId);
            properties.put("entityType", entityType);
            properties.put("layer", entity.getProperty("Layer"));
            properties.put("color", entity.getProperty("Color"));
            properties.put("linetype", entity.getProperty("Linetype"));
            
            // 获取特定类型的属性
            if ("Line".equals(entityType)) {
                ComObject startPoint = entity.getProperty("StartPoint");
                ComObject endPoint = entity.getProperty("EndPoint");
                
                properties.put("startX", startPoint.getProperty("X"));
                properties.put("startY", startPoint.getProperty("Y"));
                properties.put("startZ", startPoint.getProperty("Z"));
                properties.put("endX", endPoint.getProperty("X"));
                properties.put("endY", endPoint.getProperty("Y"));
                properties.put("endZ", endPoint.getProperty("Z"));
            } else if ("Circle".equals(entityType)) {
                ComObject centerPoint = entity.getProperty("Center");
                
                properties.put("centerX", centerPoint.getProperty("X"));
                properties.put("centerY", centerPoint.getProperty("Y"));
                properties.put("centerZ", centerPoint.getProperty("Z"));
                properties.put("radius", entity.getProperty("Radius"));
            }
            
            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("properties", properties);
            
            return Result.success(entityId, result);
        } catch (Exception e) {
            throw new AdapterException("Failed to get entity properties", e);
        }
    }
    
    /**
     * 设置图形属性
     */
    private Result setEntityProperties(Map<String, Object> params) throws AdapterException {
        try {
            // 获取参数
            String entityId = getStringParam(params, "entityId");
            @SuppressWarnings("unchecked")
            Map<String, Object> properties = (Map<String, Object>) params.get("properties");
            
            // 获取图形
            ComObject entity = getEntityById(entityId);
            if (entity == null) {
                throw new AdapterException("Entity not found: " + entityId);
            }
            
            // 设置通用属性
            if (properties.containsKey("layer")) {
                entity.setProperty("Layer", properties.get("layer"));
            }
            if (properties.containsKey("color")) {
                entity.setProperty("Color", properties.get("color"));
            }
            if (properties.containsKey("linetype")) {
                entity.setProperty("Linetype", properties.get("linetype"));
            }
            
            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            
            return Result.success(entityId, result);
        } catch (Exception e) {
            throw new AdapterException("Failed to set entity properties", e);
        }
    }
    
    /**
     * 保存图形
     */
    private Result saveDrawing(Map<String, Object> params) throws AdapterException {
        try {
            // 获取参数
            String filePath = getStringParam(params, "filePath");
            boolean overwrite = getBooleanParam(params, "overwrite", false);
            
            // 检查文件是否存在
            File file = new File(filePath);
            if (file.exists() && !overwrite) {
                throw new AdapterException("File already exists: " + filePath);
            }
            
            // 保存图形
            document.invoke("SaveAs", filePath);
            
            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            
            return Result.success(filePath, result);
        } catch (Exception e) {
            throw new AdapterException("Failed to save drawing", e);
        }
    }
    
    /**
     * 打开图形
     */
    private Result openDrawing(Map<String, Object> params) throws AdapterException {
        try {
            // 获取参数
            String filePath = getStringParam(params, "filePath");
            
            // 检查文件是否存在
            File file = new File(filePath);
            if (!file.exists()) {
                throw new AdapterException("File not found: " + filePath);
            }
            
            // 打开图形
            document = application.invoke("Documents").invoke("Open", filePath);
            
            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            
            return Result.success(filePath, result);
        } catch (Exception e) {
            throw new AdapterException("Failed to open drawing", e);
        }
    }
    
    /**
     * 创建点
     */
    private ComObject createPoint(double x, double y, double z) throws Exception {
        ComObject point = ComObjectFactory.createObject("AutoCAD.Point");
        point.setProperty("X", x);
        point.setProperty("Y", y);
        point.setProperty("Z", z);
        return point;
    }
    
    /**
     * 根据ID获取图形
     */
    private ComObject getEntityById(String entityId) throws Exception {
        // 获取图形管理器
        ComObject database = document.getProperty("Database");
        
        // 根据句柄获取图形
        return database.invoke("HandleToObject", entityId);
    }
    
    /**
     * 获取字符串参数
     */
    private String getStringParam(Map<String, Object> params, String name) throws AdapterException {
        Object value = params.get(name);
        if (value == null) {
            throw new AdapterException("Missing parameter: " + name);
        }
        return value.toString();
    }
    
    /**
     * 获取字符串参数，带默认值
     */
    private String getStringParam(Map<String, Object> params, String name, String defaultValue) {
        Object value = params.get(name);
        return value != null ? value.toString() : defaultValue;
    }
    
    /**
     * 获取双精度参数
     */
    private double getDoubleParam(Map<String, Object> params, String name) throws AdapterException {
        Object value = params.get(name);
        if (value == null) {
            throw new AdapterException("Missing parameter: " + name);
        }
        try {
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            throw new AdapterException("Invalid parameter: " + name);
        }
    }
    
    /**
     * 获取双精度参数，带默认值
     */
    private double getDoubleParam(Map<String, Object> params, String name, double defaultValue) {
        Object value = params.get(name);
        if (value == null) {
            return defaultValue;
        }
        try {
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }
    
    /**
     * 获取布尔参数，带默认值
     */
    private boolean getBooleanParam(Map<String, Object> params, String name, boolean defaultValue) {
        Object value = params.get(name);
        if (value == null) {
            return defaultValue;
        }
        return Boolean.parseBoolean(value.toString());
    }
}
```

AutoCAD组件封装考虑了以下关键点：

- **接口抽象**：将AutoCAD的复杂接口抽象为简单的组件操作，如创建线段、圆等。
- **参数验证**：对操作参数进行严格验证，确保参数的正确性。
- **错误处理**：处理各种可能的错误情况，提供有意义的错误信息。
- **资源管理**：在连接和断开连接时正确管理资源，避免资源泄漏。
- **多种适配器**：支持COM和.NET两种适配器，适应不同的集成场景。

#### 5.4.2 数据分析软件组件封装

数据分析软件是科学研究和工程计算的重要工具，我们以MATLAB为例，实现了数据分析软件的组件封装，提供了数据处理、计算和可视化等功能。
