# 面向异构专业软件的服务化集成架构研究（续）

```java
/**
 * MATLAB组件
 */
public class MatlabComponent extends AbstractAdapterComponent {
    private static final String COMPONENT_ID = "matlabComponent";
    private static final String COMPONENT_NAME = "MATLAB Component";
    private static final String COMPONENT_VERSION = "1.0.0";
    
    @Override
    protected SoftwareAdapter createAdapter() throws ComponentException {
        logger.debug("Creating MATLAB adapter");
        
        try {
            // 获取适配器类型
            String adapterType = context.getConfig().getProperties().getOrDefault("adapterType", "ENGINE").toString();
            
            // 创建适配器
            SoftwareAdapter adapter;
            if ("ENGINE".equals(adapterType)) {
                adapter = new MatlabEngineAdapter();
            } else if ("COM".equals(adapterType)) {
                adapter = new MatlabComAdapter();
            } else {
                throw new ComponentException("Unsupported adapter type: " + adapterType);
            }
            
            logger.info("Created MATLAB adapter: {}", adapterType);
            return adapter;
        } catch (Exception e) {
            logger.error("Failed to create MATLAB adapter", e);
            throw new ComponentException("Failed to create MATLAB adapter", e);
        }
    }
    
    @Override
    public List<OperationInfo> getSupportedOperations() {
        List<OperationInfo> operations = new ArrayList<>();
        
        // 执行脚本操作
        OperationInfo executeScript = new OperationInfo();
        executeScript.setName("executeScript");
        executeScript.setDescription("Execute MATLAB script");
        executeScript.setParameters(Arrays.asList(
            new ParameterInfo("script", "MATLAB script", "string", true, null, null)
        ));
        executeScript.setReturnType(new ParameterInfo("result", "Execution result", "string", true, null, null));
        operations.add(executeScript);
        
        // 执行函数操作
        OperationInfo executeFunction = new OperationInfo();
        executeFunction.setName("executeFunction");
        executeFunction.setDescription("Execute MATLAB function");
        executeFunction.setParameters(Arrays.asList(
            new ParameterInfo("function", "Function name", "string", true, null, null),
            new ParameterInfo("inputs", "Function inputs", "list", false, null, null),
            new ParameterInfo("outputCount", "Output count", "integer", false, 1, null)
        ));
        executeFunction.setReturnType(new ParameterInfo("outputs", "Function outputs", "list", true, null, null));
        operations.add(executeFunction);
        
        // 设置变量操作
        OperationInfo setVariable = new OperationInfo();
        setVariable.setName("setVariable");
        setVariable.setDescription("Set MATLAB variable");
        setVariable.setParameters(Arrays.asList(
            new ParameterInfo("name", "Variable name", "string", true, null, null),
            new ParameterInfo("value", "Variable value", "object", true, null, null)
        ));
        setVariable.setReturnType(new ParameterInfo("success", "Success", "boolean", true, null, null));
        operations.add(setVariable);
        
        // 获取变量操作
        OperationInfo getVariable = new OperationInfo();
        getVariable.setName("getVariable");
        getVariable.setDescription("Get MATLAB variable");
        getVariable.setParameters(Arrays.asList(
            new ParameterInfo("name", "Variable name", "string", true, null, null)
        ));
        getVariable.setReturnType(new ParameterInfo("value", "Variable value", "object", true, null, null));
        operations.add(getVariable);
        
        // 绘图操作
        OperationInfo plot = new OperationInfo();
        plot.setName("plot");
        plot.setDescription("Create plot");
        plot.setParameters(Arrays.asList(
            new ParameterInfo("x", "X values", "list", true, null, null),
            new ParameterInfo("y", "Y values", "list", true, null, null),
            new ParameterInfo("title", "Plot title", "string", false, "", null),
            new ParameterInfo("xlabel", "X-axis label", "string", false, "", null),
            new ParameterInfo("ylabel", "Y-axis label", "string", false, "", null),
            new ParameterInfo("savePath", "Save path", "string", false, null, null)
        ));
        plot.setReturnType(new ParameterInfo("plotId", "Plot ID", "string", true, null, null));
        operations.add(plot);
        
        // 数据分析操作
        OperationInfo analyzeData = new OperationInfo();
        analyzeData.setName("analyzeData");
        analyzeData.setDescription("Analyze data");
        analyzeData.setParameters(Arrays.asList(
            new ParameterInfo("data", "Data array", "list", true, null, null),
            new ParameterInfo("analysisType", "Analysis type", "string", true, null, 
                Arrays.asList("statistics", "fft", "regression", "clustering"))
        ));
        analyzeData.setReturnType(new ParameterInfo("result", "Analysis result", "map", true, null, null));
        operations.add(analyzeData);
        
        return operations;
    }
}

/**
 * MATLAB引擎适配器
 */
public class MatlabEngineAdapter implements SoftwareAdapter {
    private final Logger logger = LoggerFactory.getLogger(MatlabEngineAdapter.class);
    private Object engine; // MATLAB引擎对象
    private boolean connected = false;
    
    @Override
    public boolean connect(Map<String, Object> params) throws AdapterException {
        logger.debug("Connecting to MATLAB Engine");
        
        try {
            // 获取MATLAB引擎类
            Class<?> engineClass = Class.forName("com.mathworks.engine.MatlabEngine");
            
            // 启动MATLAB引擎
            Method startMethod = engineClass.getMethod("startMatlab");
            engine = startMethod.invoke(null);
            
            connected = true;
            logger.info("Connected to MATLAB Engine");
            return true;
        } catch (Exception e) {
            logger.error("Failed to connect to MATLAB Engine", e);
            throw new AdapterException("Failed to connect to MATLAB Engine", e);
        }
    }
    
    @Override
    public Result executeOperation(String operationName, Map<String, Object> params) throws AdapterException {
        logger.debug("Executing MATLAB operation: {}", operationName);
        
        if (!connected) {
            throw new AdapterException("Not connected to MATLAB");
        }
        
        try {
            switch (operationName) {
                case "executeScript":
                    return executeScript(params);
                case "executeFunction":
                    return executeFunction(params);
                case "setVariable":
                    return setVariable(params);
                case "getVariable":
                    return getVariable(params);
                case "plot":
                    return plot(params);
                case "analyzeData":
                    return analyzeData(params);
                default:
                    throw new AdapterException("Unsupported operation: " + operationName);
            }
        } catch (Exception e) {
            logger.error("Failed to execute MATLAB operation: {}", operationName, e);
            throw new AdapterException("Failed to execute MATLAB operation: " + operationName, e);
        }
    }
    
    @Override
    public void disconnect() throws AdapterException {
        logger.debug("Disconnecting from MATLAB Engine");
        
        try {
            if (engine != null) {
                // 关闭MATLAB引擎
                Method closeMethod = engine.getClass().getMethod("close");
                closeMethod.invoke(engine);
                engine = null;
            }
            
            connected = false;
            logger.info("Disconnected from MATLAB Engine");
        } catch (Exception e) {
            logger.error("Failed to disconnect from MATLAB Engine", e);
            throw new AdapterException("Failed to disconnect from MATLAB Engine", e);
        }
    }
    
    /**
     * 执行MATLAB脚本
     */
    private Result executeScript(Map<String, Object> params) throws AdapterException {
        try {
            // 获取参数
            String script = getStringParam(params, "script");
            
            // 执行脚本
            Method evalMethod = engine.getClass().getMethod("eval", String.class);
            evalMethod.invoke(engine, script);
            
            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("result", "Script executed successfully");
            
            return Result.success("Script executed", result);
        } catch (Exception e) {
            throw new AdapterException("Failed to execute script", e);
        }
    }
    
    /**
     * 执行MATLAB函数
     */
    private Result executeFunction(Map<String, Object> params) throws AdapterException {
        try {
            // 获取参数
            String functionName = getStringParam(params, "function");
            @SuppressWarnings("unchecked")
            List<Object> inputs = (List<Object>) params.getOrDefault("inputs", Collections.emptyList());
            int outputCount = getIntParam(params, "outputCount", 1);
            
            // 执行函数
            Method feval = engine.getClass().getMethod("feval", String.class, int.class, Object[].class);
            Object[] outputs = (Object[]) feval.invoke(engine, functionName, outputCount, inputs.toArray());
            
            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("outputs", Arrays.asList(outputs));
            
            return Result.success(functionName, result);
        } catch (Exception e) {
            throw new AdapterException("Failed to execute function", e);
        }
    }
    
    /**
     * 设置MATLAB变量
     */
    private Result setVariable(Map<String, Object> params) throws AdapterException {
        try {
            // 获取参数
            String name = getStringParam(params, "name");
            Object value = params.get("value");
            
            // 设置变量
            Method putVariableMethod = engine.getClass().getMethod("putVariable", String.class, Object.class);
            putVariableMethod.invoke(engine, name, value);
            
            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            
            return Result.success(name, result);
        } catch (Exception e) {
            throw new AdapterException("Failed to set variable", e);
        }
    }
    
    /**
     * 获取MATLAB变量
     */
    private Result getVariable(Map<String, Object> params) throws AdapterException {
        try {
            // 获取参数
            String name = getStringParam(params, "name");
            
            // 获取变量
            Method getVariableMethod = engine.getClass().getMethod("getVariable", String.class);
            Object value = getVariableMethod.invoke(engine, name);
            
            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("value", value);
            
            return Result.success(name, result);
        } catch (Exception e) {
            throw new AdapterException("Failed to get variable", e);
        }
    }
    
    /**
     * 创建绘图
     */
    private Result plot(Map<String, Object> params) throws AdapterException {
        try {
            // 获取参数
            @SuppressWarnings("unchecked")
            List<Double> x = (List<Double>) params.get("x");
            @SuppressWarnings("unchecked")
            List<Double> y = (List<Double>) params.get("y");
            String title = getStringParam(params, "title", "");
            String xlabel = getStringParam(params, "xlabel", "");
            String ylabel = getStringParam(params, "ylabel", "");
            String savePath = getStringParam(params, "savePath", null);
            
            // 设置数据
            setVariable(Map.of("name", "x", "value", x.toArray()));
            setVariable(Map.of("name", "y", "value", y.toArray()));
            
            // 创建绘图
            StringBuilder script = new StringBuilder();
            script.append("figure('Visible', 'off');");
            script.append("plot(x, y);");
            
            if (!title.isEmpty()) {
                script.append("title('").append(title).append("');");
            }
            if (!xlabel.isEmpty()) {
                script.append("xlabel('").append(xlabel).append("');");
            }
            if (!ylabel.isEmpty()) {
                script.append("ylabel('").append(ylabel).append("');");
            }
            
            // 生成唯一ID
            String plotId = UUID.randomUUID().toString();
            
            // 保存绘图
            if (savePath != null) {
                script.append("saveas(gcf, '").append(savePath).append("');");
            }
            
            // 执行脚本
            executeScript(Map.of("script", script.toString()));
            
            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("plotId", plotId);
            if (savePath != null) {
                result.put("savePath", savePath);
            }
            
            return Result.success(plotId, result);
        } catch (Exception e) {
            throw new AdapterException("Failed to create plot", e);
        }
    }
    
    /**
     * 分析数据
     */
    private Result analyzeData(Map<String, Object> params) throws AdapterException {
        try {
            // 获取参数
            @SuppressWarnings("unchecked")
            List<Double> data = (List<Double>) params.get("data");
            String analysisType = getStringParam(params, "analysisType");
            
            // 设置数据
            setVariable(Map.of("name", "data", "value", data.toArray()));
            
            // 执行分析
            StringBuilder script = new StringBuilder();
            Map<String, Object> result = new HashMap<>();
            
            switch (analysisType) {
                case "statistics":
                    script.append("stats = struct();");
                    script.append("stats.mean = mean(data);");
                    script.append("stats.median = median(data);");
                    script.append("stats.std = std(data);");
                    script.append("stats.min = min(data);");
                    script.append("stats.max = max(data);");
                    
                    // 执行脚本
                    executeScript(Map.of("script", script.toString()));
                    
                    // 获取结果
                    @SuppressWarnings("unchecked")
                    Map<String, Object> stats = (Map<String, Object>) getVariable(Map.of("name", "stats")).getData().get("value");
                    result.put("statistics", stats);
                    break;
                    
                case "fft":
                    script.append("fftResult = fft(data);");
                    script.append("fftMag = abs(fftResult);");
                    
                    // 执行脚本
                    executeScript(Map.of("script", script.toString()));
                    
                    // 获取结果
                    Object fftMag = getVariable(Map.of("name", "fftMag")).getData().get("value");
                    result.put("fft", fftMag);
                    break;
                    
                case "regression":
                    script.append("x = (1:length(data))';");
                    script.append("p = polyfit(x, data, 1);");
                    script.append("yfit = polyval(p, x);");
                    script.append("reg = struct();");
                    script.append("reg.slope = p(1);");
                    script.append("reg.intercept = p(2);");
                    script.append("reg.fitted = yfit;");
                    
                    // 执行脚本
                    executeScript(Map.of("script", script.toString()));
                    
                    // 获取结果
                    @SuppressWarnings("unchecked")
                    Map<String, Object> reg = (Map<String, Object>) getVariable(Map.of("name", "reg")).getData().get("value");
                    result.put("regression", reg);
                    break;
                    
                case "clustering":
                    script.append("X = data(:);");
                    script.append("[idx, centroids] = kmeans(X, 2);");
                    script.append("clust = struct();");
                    script.append("clust.idx = idx;");
                    script.append("clust.centroids = centroids;");
                    
                    // 执行脚本
                    executeScript(Map.of("script", script.toString()));
                    
                    // 获取结果
                    @SuppressWarnings("unchecked")
                    Map<String, Object> clust = (Map<String, Object>) getVariable(Map.of("name", "clust")).getData().get("value");
                    result.put("clustering", clust);
                    break;
                    
                default:
                    throw new AdapterException("Unsupported analysis type: " + analysisType);
            }
            
            return Result.success(analysisType, result);
        } catch (Exception e) {
            throw new AdapterException("Failed to analyze data", e);
        }
    }
    
    /**
     * 获取字符串参数
     */
    private String getStringParam(Map<String, Object> params, String name) throws AdapterException {
        Object value = params.get(name);
        if (value == null) {
            throw new AdapterException("Missing parameter: " + name);
        }
        return value.toString();
    }
    
    /**
     * 获取字符串参数，带默认值
     */
    private String getStringParam(Map<String, Object> params, String name, String defaultValue) {
        Object value = params.get(name);
        return value != null ? value.toString() : defaultValue;
    }
    
    /**
     * 获取整数参数，带默认值
     */
    private int getIntParam(Map<String, Object> params, String name, int defaultValue) {
        Object value = params.get(name);
        if (value == null) {
            return defaultValue;
        }
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }
}
```

MATLAB组件封装考虑了以下关键点：

- **功能抽象**：将MATLAB的复杂功能抽象为简单的组件操作，如执行脚本、函数、绘图和数据分析等。
- **数据转换**：处理Java和MATLAB之间的数据类型转换，确保数据的正确传递。
- **多种适配器**：支持引擎和COM两种适配器，适应不同的集成场景。
- **资源管理**：在连接和断开连接时正确管理MATLAB引擎资源，避免资源泄漏。
- **错误处理**：处理各种可能的错误情况，提供有意义的错误信息。
