# 面向异构专业软件的服务化集成架构研究（续）

### 5.2 组件注册与发现

组件注册与发现是组件封装层的重要功能，负责管理组件的注册、查询和加载，支持组件的动态发现和使用。

#### 5.2.1 组件注册表设计

组件注册表是组件管理的核心，维护了组件的元数据和状态信息，支持组件的注册、查询和加载。

```java
/**
 * 组件注册表接口
 */
public interface ComponentRegistry {
    
    /**
     * 注册组件描述符
     * @param descriptor 组件描述符
     * @throws ComponentRegistryException 注册异常
     */
    void registerComponent(ComponentDescriptor descriptor) throws ComponentRegistryException;
    
    /**
     * 注销组件
     * @param componentId 组件ID
     * @throws ComponentRegistryException 注销异常
     */
    void unregisterComponent(String componentId) throws ComponentRegistryException;
    
    /**
     * 获取组件描述符
     * @param componentId 组件ID
     * @return 组件描述符，如果不存在则返回null
     */
    ComponentDescriptor getComponentDescriptor(String componentId);
    
    /**
     * 获取所有组件描述符
     * @return 组件描述符列表
     */
    List<ComponentDescriptor> getAllComponentDescriptors();
    
    /**
     * 根据类型获取组件描述符
     * @param type 组件类型
     * @return 组件描述符列表
     */
    List<ComponentDescriptor> getComponentDescriptorsByType(ComponentType type);
    
    /**
     * 根据标签获取组件描述符
     * @param tag 标签
     * @return 组件描述符列表
     */
    List<ComponentDescriptor> getComponentDescriptorsByTag(String tag);
    
    /**
     * 根据条件查询组件描述符
     * @param filter 过滤条件
     * @return 组件描述符列表
     */
    List<ComponentDescriptor> findComponentDescriptors(ComponentFilter filter);
    
    /**
     * 检查组件是否存在
     * @param componentId 组件ID
     * @return 是否存在
     */
    boolean hasComponent(String componentId);
    
    /**
     * 获取组件依赖
     * @param componentId 组件ID
     * @return 依赖组件ID列表
     * @throws ComponentRegistryException 获取依赖异常
     */
    List<String> getComponentDependencies(String componentId) throws ComponentRegistryException;
    
    /**
     * 获取依赖该组件的组件
     * @param componentId 组件ID
     * @return 依赖该组件的组件ID列表
     * @throws ComponentRegistryException 获取依赖异常
     */
    List<String> getDependentComponents(String componentId) throws ComponentRegistryException;
    
    /**
     * 验证组件依赖
     * @param componentId 组件ID
     * @throws ComponentRegistryException 依赖验证异常
     */
    void validateDependencies(String componentId) throws ComponentRegistryException;
}

/**
 * 组件过滤器接口
 */
public interface ComponentFilter {
    
    /**
     * 判断组件是否匹配过滤条件
     * @param descriptor 组件描述符
     * @return 是否匹配
     */
    boolean matches(ComponentDescriptor descriptor);
}

/**
 * 组件注册表异常
 */
public class ComponentRegistryException extends Exception {
    
    public ComponentRegistryException(String message) {
        super(message);
    }
    
    public ComponentRegistryException(String message, Throwable cause) {
        super(message, cause);
    }
}

/**
 * 默认组件注册表实现
 */
public class DefaultComponentRegistry implements ComponentRegistry {
    private final Map<String, ComponentDescriptor> components = new ConcurrentHashMap<>();
    private final Map<ComponentType, Set<String>> componentsByType = new ConcurrentHashMap<>();
    private final Map<String, Set<String>> componentsByTag = new ConcurrentHashMap<>();
    private final Map<String, Set<String>> dependencyGraph = new ConcurrentHashMap<>();
    private final Map<String, Set<String>> reverseDependencyGraph = new ConcurrentHashMap<>();
    private final Logger logger = LoggerFactory.getLogger(DefaultComponentRegistry.class);
    
    @Override
    public void registerComponent(ComponentDescriptor descriptor) throws ComponentRegistryException {
        String componentId = descriptor.getId();
        logger.debug("Registering component: {}", componentId);
        
        // 检查组件ID是否已存在
        if (components.containsKey(componentId)) {
            throw new ComponentRegistryException("Component already registered: " + componentId);
        }
        
        // 验证组件描述符
        validateComponentDescriptor(descriptor);
        
        // 注册组件描述符
        components.put(componentId, descriptor);
        
        // 更新类型索引
        componentsByType.computeIfAbsent(descriptor.getType(), k -> new HashSet<>())
                .add(componentId);
        
        // 更新标签索引
        if (descriptor.getTags() != null) {
            for (String tag : descriptor.getTags().keySet()) {
                componentsByTag.computeIfAbsent(tag, k -> new HashSet<>())
                        .add(componentId);
            }
        }
        
        // 更新依赖图
        updateDependencyGraphs(descriptor);
        
        logger.info("Component registered: {}", componentId);
    }
    
    @Override
    public void unregisterComponent(String componentId) throws ComponentRegistryException {
        logger.debug("Unregistering component: {}", componentId);
        
        // 检查组件是否存在
        ComponentDescriptor descriptor = getComponentDescriptor(componentId);
        if (descriptor == null) {
            throw new ComponentRegistryException("Component not found: " + componentId);
        }
        
        // 检查是否有其他组件依赖该组件
        Set<String> dependents = reverseDependencyGraph.get(componentId);
        if (dependents != null && !dependents.isEmpty()) {
            throw new ComponentRegistryException("Cannot unregister component with dependents: " + componentId);
        }
        
        // 移除组件描述符
        components.remove(componentId);
        
        // 更新类型索引
        Set<String> typeComponents = componentsByType.get(descriptor.getType());
        if (typeComponents != null) {
            typeComponents.remove(componentId);
        }
        
        // 更新标签索引
        if (descriptor.getTags() != null) {
            for (String tag : descriptor.getTags().keySet()) {
                Set<String> tagComponents = componentsByTag.get(tag);
                if (tagComponents != null) {
                    tagComponents.remove(componentId);
                }
            }
        }
        
        // 更新依赖图
        dependencyGraph.remove(componentId);
        reverseDependencyGraph.remove(componentId);
        
        logger.info("Component unregistered: {}", componentId);
    }
    
    @Override
    public ComponentDescriptor getComponentDescriptor(String componentId) {
        return components.get(componentId);
    }
    
    @Override
    public List<ComponentDescriptor> getAllComponentDescriptors() {
        return new ArrayList<>(components.values());
    }
    
    @Override
    public List<ComponentDescriptor> getComponentDescriptorsByType(ComponentType type) {
        Set<String> componentIds = componentsByType.get(type);
        if (componentIds == null) {
            return Collections.emptyList();
        }
        
        List<ComponentDescriptor> descriptors = new ArrayList<>();
        for (String componentId : componentIds) {
            ComponentDescriptor descriptor = components.get(componentId);
            if (descriptor != null) {
                descriptors.add(descriptor);
            }
        }
        
        return descriptors;
    }
    
    @Override
    public List<ComponentDescriptor> getComponentDescriptorsByTag(String tag) {
        Set<String> componentIds = componentsByTag.get(tag);
        if (componentIds == null) {
            return Collections.emptyList();
        }
        
        List<ComponentDescriptor> descriptors = new ArrayList<>();
        for (String componentId : componentIds) {
            ComponentDescriptor descriptor = components.get(componentId);
            if (descriptor != null) {
                descriptors.add(descriptor);
            }
        }
        
        return descriptors;
    }
    
    @Override
    public List<ComponentDescriptor> findComponentDescriptors(ComponentFilter filter) {
        List<ComponentDescriptor> descriptors = new ArrayList<>();
        
        for (ComponentDescriptor descriptor : components.values()) {
            if (filter.matches(descriptor)) {
                descriptors.add(descriptor);
            }
        }
        
        return descriptors;
    }
    
    @Override
    public boolean hasComponent(String componentId) {
        return components.containsKey(componentId);
    }
    
    @Override
    public List<String> getComponentDependencies(String componentId) throws ComponentRegistryException {
        Set<String> dependencies = dependencyGraph.get(componentId);
        if (dependencies == null) {
            return Collections.emptyList();
        }
        return new ArrayList<>(dependencies);
    }
    
    @Override
    public List<String> getDependentComponents(String componentId) throws ComponentRegistryException {
        Set<String> dependents = reverseDependencyGraph.get(componentId);
        if (dependents == null) {
            return Collections.emptyList();
        }
        return new ArrayList<>(dependents);
    }
    
    @Override
    public void validateDependencies(String componentId) throws ComponentRegistryException {
        ComponentDescriptor descriptor = getComponentDescriptor(componentId);
        if (descriptor == null) {
            throw new ComponentRegistryException("Component not found: " + componentId);
        }
        
        // 检查依赖是否存在
        for (DependencyDescriptor dependency : descriptor.getDependencies()) {
            String dependencyType = dependency.getComponentType();
            boolean found = false;
            
            // 查找匹配的组件
            for (ComponentDescriptor candidate : components.values()) {
                if (candidate.getType().name().equals(dependencyType)) {
                    // 检查版本兼容性
                    if (isVersionCompatible(candidate.getVersion(), dependency.getMinVersion(), dependency.getMaxVersion())) {
                        found = true;
                        break;
                    }
                }
            }
            
            // 如果是必需依赖且未找到，则抛出异常
            if (!found && dependency.isRequired()) {
                throw new ComponentRegistryException("Required dependency not found: " + dependencyType + 
                        " for component: " + componentId);
            }
        }
    }
    
    /**
     * 验证组件描述符
     * @param descriptor 组件描述符
     * @throws ComponentRegistryException 验证异常
     */
    private void validateComponentDescriptor(ComponentDescriptor descriptor) throws ComponentRegistryException {
        // 检查必要字段
        if (descriptor.getId() == null || descriptor.getId().isEmpty()) {
            throw new ComponentRegistryException("Component ID is required");
        }
        
        if (descriptor.getName() == null || descriptor.getName().isEmpty()) {
            throw new ComponentRegistryException("Component name is required");
        }
        
        if (descriptor.getType() == null) {
            throw new ComponentRegistryException("Component type is required");
        }
        
        if (descriptor.getImplementationClass() == null || descriptor.getImplementationClass().isEmpty()) {
            throw new ComponentRegistryException("Implementation class is required");
        }
        
        // 检查实现类是否存在
        try {
            Class.forName(descriptor.getImplementationClass());
        } catch (ClassNotFoundException e) {
            throw new ComponentRegistryException("Implementation class not found: " + descriptor.getImplementationClass(), e);
        }
    }
    
    /**
     * 更新依赖图
     * @param descriptor 组件描述符
     */
    private void updateDependencyGraphs(ComponentDescriptor descriptor) {
        String componentId = descriptor.getId();
        Set<String> dependencies = new HashSet<>();
        
        // 收集依赖
        for (DependencyDescriptor dependency : descriptor.getDependencies()) {
            String dependencyType = dependency.getComponentType();
            
            // 查找匹配的组件
            for (ComponentDescriptor candidate : components.values()) {
                if (candidate.getType().name().equals(dependencyType)) {
                    // 检查版本兼容性
                    if (isVersionCompatible(candidate.getVersion(), dependency.getMinVersion(), dependency.getMaxVersion())) {
                        dependencies.add(candidate.getId());
                        
                        // 更新反向依赖图
                        reverseDependencyGraph.computeIfAbsent(candidate.getId(), k -> new HashSet<>())
                                .add(componentId);
                    }
                }
            }
        }
        
        // 更新依赖图
        dependencyGraph.put(componentId, dependencies);
    }
    
    /**
     * 检查版本兼容性
     * @param version 版本
     * @param minVersion 最小版本
     * @param maxVersion 最大版本
     * @return 是否兼容
     */
    private boolean isVersionCompatible(String version, String minVersion, String maxVersion) {
        // 如果未指定版本要求，则认为兼容
        if ((minVersion == null || minVersion.isEmpty()) && (maxVersion == null || maxVersion.isEmpty())) {
            return true;
        }
        
        // 解析版本
        Version v = parseVersion(version);
        
        // 检查最小版本
        if (minVersion != null && !minVersion.isEmpty()) {
            Version min = parseVersion(minVersion);
            if (v.compareTo(min) < 0) {
                return false;
            }
        }
        
        // 检查最大版本
        if (maxVersion != null && !maxVersion.isEmpty()) {
            Version max = parseVersion(maxVersion);
            if (v.compareTo(max) > 0) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 解析版本字符串
     * @param version 版本字符串
     * @return 版本对象
     */
    private Version parseVersion(String version) {
        // 简化实现，实际可能需要更复杂的版本解析逻辑
        String[] parts = version.split("\\.");
        int major = parts.length > 0 ? Integer.parseInt(parts[0]) : 0;
        int minor = parts.length > 1 ? Integer.parseInt(parts[1]) : 0;
        int patch = parts.length > 2 ? Integer.parseInt(parts[2]) : 0;
        
        return new Version(major, minor, patch);
    }
    
    /**
     * 版本类
     */
    private static class Version implements Comparable<Version> {
        private final int major;
        private final int minor;
        private final int patch;
        
        public Version(int major, int minor, int patch) {
            this.major = major;
            this.minor = minor;
            this.patch = patch;
        }
        
        @Override
        public int compareTo(Version other) {
            if (major != other.major) {
                return Integer.compare(major, other.major);
            }
            if (minor != other.minor) {
                return Integer.compare(minor, other.minor);
            }
            return Integer.compare(patch, other.patch);
        }
    }
}
```

组件注册表设计考虑了以下关键点：

- **组件注册**：支持组件的注册和注销，维护组件的元数据信息。
- **组件查询**：提供多种查询方式，包括ID、类型、标签和自定义条件等，支持组件的灵活查找。
- **依赖管理**：维护组件之间的依赖关系，支持依赖的验证和解析。
- **版本管理**：支持组件的版本管理，确保组件版本的兼容性。
- **索引优化**：使用多种索引结构，提高查询效率。

#### 5.2.2 组件加载器设计

组件加载器负责从不同来源加载组件描述符，支持组件的动态发现和加载。

```java
/**
 * 组件加载器接口
 */
public interface ComponentLoader {
    
    /**
     * 加载组件描述符
     * @return 组件描述符列表
     * @throws ComponentLoaderException 加载异常
     */
    List<ComponentDescriptor> loadComponents() throws ComponentLoaderException;
    
    /**
     * 获取加载器名称
     * @return 加载器名称
     */
    String getName();
    
    /**
     * 获取加载器描述
     * @return 加载器描述
     */
    String getDescription();
}

/**
 * 组件加载器异常
 */
public class ComponentLoaderException extends Exception {
    
    public ComponentLoaderException(String message) {
        super(message);
    }
    
    public ComponentLoaderException(String message, Throwable cause) {
        super(message, cause);
    }
}

/**
 * 类路径组件加载器
 */
public class ClasspathComponentLoader implements ComponentLoader {
    private final String basePackage;
    private final ResourcePatternResolver resourceResolver;
    private final ObjectMapper objectMapper;
    private final Logger logger = LoggerFactory.getLogger(ClasspathComponentLoader.class);
    
    public ClasspathComponentLoader(String basePackage) {
        this.basePackage = basePackage;
        this.resourceResolver = new PathMatchingResourcePatternResolver();
        this.objectMapper = new ObjectMapper();
    }
    
    @Override
    public List<ComponentDescriptor> loadComponents() throws ComponentLoaderException {
        logger.debug("Loading components from classpath: {}", basePackage);
        
        List<ComponentDescriptor> descriptors = new ArrayList<>();
        
        try {
            // 查找组件描述符文件
            String locationPattern = "classpath*:" + basePackage.replace('.', '/') + "/**/*.component.json";
            Resource[] resources = resourceResolver.getResources(locationPattern);
            
            logger.debug("Found {} component descriptor files", resources.length);
            
            // 解析组件描述符
            for (Resource resource : resources) {
                try {
                    ComponentDescriptor descriptor = parseComponentDescriptor(resource);
                    descriptors.add(descriptor);
                    logger.debug("Loaded component: {} from {}", descriptor.getId(), resource.getURL());
                } catch (Exception e) {
                    logger.error("Failed to parse component descriptor: {}", resource.getURL(), e);
                }
            }
            
            logger.info("Loaded {} components from classpath", descriptors.size());
            return descriptors;
        } catch (Exception e) {
            logger.error("Failed to load components from classpath", e);
            throw new ComponentLoaderException("Failed to load components from classpath", e);
        }
    }
    
    /**
     * 解析组件描述符
     * @param resource 资源
     * @return 组件描述符
     * @throws IOException IO异常
     */
    private ComponentDescriptor parseComponentDescriptor(Resource resource) throws IOException {
        try (InputStream is = resource.getInputStream()) {
            return objectMapper.readValue(is, ComponentDescriptor.class);
        }
    }
    
    @Override
    public String getName() {
        return "ClasspathComponentLoader";
    }
    
    @Override
    public String getDescription() {
        return "Loads component descriptors from classpath resources";
    }
}

/**
 * 文件系统组件加载器
 */
public class FileSystemComponentLoader implements ComponentLoader {
    private final Path directory;
    private final ObjectMapper objectMapper;
    private final Logger logger = LoggerFactory.getLogger(FileSystemComponentLoader.class);
    
    public FileSystemComponentLoader(Path directory) {
        this.directory = directory;
        this.objectMapper = new ObjectMapper();
    }
    
    @Override
    public List<ComponentDescriptor> loadComponents() throws ComponentLoaderException {
        logger.debug("Loading components from directory: {}", directory);
        
        List<ComponentDescriptor> descriptors = new ArrayList<>();
        
        try {
            // 检查目录是否存在
            if (!Files.exists(directory) || !Files.isDirectory(directory)) {
                logger.warn("Component directory does not exist or is not a directory: {}", directory);
                return descriptors;
            }
            
            // 查找组件描述符文件
            try (Stream<Path> paths = Files.walk(directory)) {
                List<Path> componentFiles = paths
                        .filter(Files::isRegularFile)
                        .filter(path -> path.getFileName().toString().endsWith(".component.json"))
                        .collect(Collectors.toList());
                
                logger.debug("Found {} component descriptor files", componentFiles.size());
                
                // 解析组件描述符
                for (Path file : componentFiles) {
                    try {
                        ComponentDescriptor descriptor = parseComponentDescriptor(file);
                        descriptors.add(descriptor);
                        logger.debug("Loaded component: {} from {}", descriptor.getId(), file);
                    } catch (Exception e) {
                        logger.error("Failed to parse component descriptor: {}", file, e);
                    }
                }
            }
            
            logger.info("Loaded {} components from directory", descriptors.size());
            return descriptors;
        } catch (Exception e) {
            logger.error("Failed to load components from directory", e);
            throw new ComponentLoaderException("Failed to load components from directory", e);
        }
    }
    
    /**
     * 解析组件描述符
     * @param file 文件
     * @return 组件描述符
     * @throws IOException IO异常
     */
    private ComponentDescriptor parseComponentDescriptor(Path file) throws IOException {
        try (InputStream is = Files.newInputStream(file)) {
            return objectMapper.readValue(is, ComponentDescriptor.class);
        }
    }
    
    @Override
    public String getName() {
        return "FileSystemComponentLoader";
    }
    
    @Override
    public String getDescription() {
        return "Loads component descriptors from file system directory";
    }
}
```

组件加载器设计考虑了以下关键点：

- **多源加载**：支持从类路径、文件系统等多种来源加载组件描述符，提高灵活性。
- **描述符解析**：解析JSON格式的组件描述符，支持组件元数据的标准化表示。
- **错误处理**：处理加载和解析过程中的异常，确保加载过程的稳定性。
- **日志记录**：记录加载过程的详细日志，便于问题排查。
