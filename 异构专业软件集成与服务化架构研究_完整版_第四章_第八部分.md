# 面向异构专业软件的服务化集成架构研究（续）

#### 4.2.4 监控与管理

为了确保调度层的稳定运行和及时发现问题，我们实现了全面的监控与管理机制，包括性能指标收集、健康检查和管理接口等。

**1. 性能指标收集**

性能指标收集是监控系统运行状态的基础，我们使用Micrometer框架收集各种性能指标，并通过Prometheus存储和展示。

```java
/**
 * 调度层指标收集器
 */
public class SchedulerMetricsCollector {
    private final MeterRegistry registry;
    private final TaskQueueManager queueManager;
    private final AdapterRegistry adapterRegistry;
    private final TaskRepository taskRepository;
    private final ScheduledExecutorService scheduler;
    private final Logger logger = LoggerFactory.getLogger(SchedulerMetricsCollector.class);
    
    // 指标计数器和计时器
    private final Counter taskSubmittedCounter;
    private final Counter taskCompletedCounter;
    private final Counter taskFailedCounter;
    private final Timer taskExecutionTimer;
    private final Map<String, Gauge> queueSizeGauges = new HashMap<>();
    private final Map<String, Gauge> adapterStatusGauges = new HashMap<>();
    
    public SchedulerMetricsCollector(MeterRegistry registry, TaskQueueManager queueManager,
                                    AdapterRegistry adapterRegistry, TaskRepository taskRepository) {
        this.registry = registry;
        this.queueManager = queueManager;
        this.adapterRegistry = adapterRegistry;
        this.taskRepository = taskRepository;
        this.scheduler = Executors.newSingleThreadScheduledExecutor();
        
        // 创建计数器和计时器
        this.taskSubmittedCounter = Counter.builder("scheduler.tasks.submitted")
                .description("Number of tasks submitted")
                .register(registry);
        
        this.taskCompletedCounter = Counter.builder("scheduler.tasks.completed")
                .description("Number of tasks completed successfully")
                .register(registry);
        
        this.taskFailedCounter = Counter.builder("scheduler.tasks.failed")
                .description("Number of tasks failed")
                .register(registry);
        
        this.taskExecutionTimer = Timer.builder("scheduler.tasks.execution.time")
                .description("Task execution time")
                .register(registry);
        
        // 初始化队列大小指标
        initQueueSizeGauges();
        
        // 初始化适配器状态指标
        initAdapterStatusGauges();
        
        logger.info("Scheduler metrics collector initialized");
    }
    
    /**
     * 启动指标收集
     */
    public void start() {
        logger.info("Starting scheduler metrics collection");
        
        // 定期更新指标
        scheduler.scheduleAtFixedDelay(this::updateMetrics, 0, 10, TimeUnit.SECONDS);
    }
    
    /**
     * 停止指标收集
     */
    public void stop() {
        logger.info("Stopping scheduler metrics collection");
        
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            scheduler.shutdownNow();
        }
    }
    
    /**
     * 初始化队列大小指标
     */
    private void initQueueSizeGauges() {
        // 全局队列大小
        queueSizeGauges.put("global", Gauge.builder("scheduler.queue.size", 
                () -> queueManager.getQueueStats().getGlobalQueueSize())
                .description("Global queue size")
                .tag("queue", "global")
                .register(registry));
        
        // 延迟队列大小
        queueSizeGauges.put("delay", Gauge.builder("scheduler.queue.size", 
                () -> queueManager.getQueueStats().getDelayQueueSize())
                .description("Delay queue size")
                .tag("queue", "delay")
                .register(registry));
    }
    
    /**
     * 初始化适配器状态指标
     */
    private void initAdapterStatusGauges() {
        // 适配器状态将在updateMetrics中动态更新
    }
    
    /**
     * 更新指标
     */
    private void updateMetrics() {
        try {
            // 更新队列大小指标
            updateQueueSizeMetrics();
            
            // 更新适配器状态指标
            updateAdapterStatusMetrics();
            
            // 更新任务统计指标
            updateTaskStatisticsMetrics();
        } catch (Exception e) {
            logger.error("Error updating metrics", e);
        }
    }
    
    /**
     * 更新队列大小指标
     */
    private void updateQueueSizeMetrics() {
        // 获取适配器队列大小
        Map<String, Integer> adapterQueueSizes = queueManager.getQueueStats().getAdapterQueueSizes();
        
        // 更新或创建适配器队列大小指标
        for (Map.Entry<String, Integer> entry : adapterQueueSizes.entrySet()) {
            String adapterId = entry.getKey();
            String gaugeKey = "adapter_" + adapterId;
            
            if (!queueSizeGauges.containsKey(gaugeKey)) {
                queueSizeGauges.put(gaugeKey, Gauge.builder("scheduler.queue.size", 
                        () -> queueManager.getQueueStats().getAdapterQueueSizes().getOrDefault(adapterId, 0))
                        .description("Adapter queue size")
                        .tag("queue", "adapter")
                        .tag("adapter", adapterId)
                        .register(registry));
            }
        }
    }
    
    /**
     * 更新适配器状态指标
     */
    private void updateAdapterStatusMetrics() {
        // 获取所有适配器ID
        List<String> adapterIds = adapterRegistry.getAllAdapterIds();
        
        // 更新或创建适配器状态指标
        for (String adapterId : adapterIds) {
            if (!adapterStatusGauges.containsKey(adapterId)) {
                adapterStatusGauges.put(adapterId, Gauge.builder("scheduler.adapter.status", 
                        () -> getAdapterStatusValue(adapterRegistry.getAdapterStatus(adapterId)))
                        .description("Adapter status")
                        .tag("adapter", adapterId)
                        .register(registry));
            }
        }
    }
    
    /**
     * 获取适配器状态值
     */
    private int getAdapterStatusValue(AdapterStatus status) {
        switch (status) {
            case AVAILABLE:
                return 1;
            case UNAVAILABLE:
                return 0;
            case DEGRADED:
                return 2;
            default:
                return -1;
        }
    }
    
    /**
     * 更新任务统计指标
     */
    private void updateTaskStatisticsMetrics() {
        // 获取最近一小时的任务统计
        Date oneHourAgo = new Date(System.currentTimeMillis() - 3600000);
        
        // 统计各状态任务数量
        Map<TaskStatus, Long> taskCounts = taskRepository.countByStatusAndCreateTimeAfter(oneHourAgo);
        
        // 更新任务状态指标
        for (Map.Entry<TaskStatus, Long> entry : taskCounts.entrySet()) {
            TaskStatus status = entry.getKey();
            Long count = entry.getValue();
            
            Gauge.builder("scheduler.tasks.count", () -> count)
                    .description("Task count by status")
                    .tag("status", status.name())
                    .register(registry);
        }
    }
    
    /**
     * 记录任务提交
     */
    public void recordTaskSubmitted() {
        taskSubmittedCounter.increment();
    }
    
    /**
     * 记录任务完成
     */
    public void recordTaskCompleted() {
        taskCompletedCounter.increment();
    }
    
    /**
     * 记录任务失败
     */
    public void recordTaskFailed() {
        taskFailedCounter.increment();
    }
    
    /**
     * 记录任务执行时间
     */
    public void recordTaskExecutionTime(long timeMs) {
        taskExecutionTimer.record(timeMs, TimeUnit.MILLISECONDS);
    }
    
    /**
     * 获取指标摘要
     */
    public MetricsSummary getMetricsSummary() {
        MetricsSummary summary = new MetricsSummary();
        
        // 任务计数
        summary.setTasksSubmitted((long) taskSubmittedCounter.count());
        summary.setTasksCompleted((long) taskCompletedCounter.count());
        summary.setTasksFailed((long) taskFailedCounter.count());
        
        // 任务执行时间
        summary.setTaskExecutionTimeAvg(taskExecutionTimer.mean(TimeUnit.MILLISECONDS));
        summary.setTaskExecutionTimeMax(taskExecutionTimer.max(TimeUnit.MILLISECONDS));
        summary.setTaskExecutionTime95th(taskExecutionTimer.percentile(0.95, TimeUnit.MILLISECONDS));
        
        // 队列大小
        QueueStats queueStats = queueManager.getQueueStats();
        summary.setGlobalQueueSize(queueStats.getGlobalQueueSize());
        summary.setDelayQueueSize(queueStats.getDelayQueueSize());
        summary.setAdapterQueueSizes(queueStats.getAdapterQueueSizes());
        
        // 适配器状态
        Map<String, AdapterStatus> adapterStatuses = new HashMap<>();
        for (String adapterId : adapterRegistry.getAllAdapterIds()) {
            adapterStatuses.put(adapterId, adapterRegistry.getAdapterStatus(adapterId));
        }
        summary.setAdapterStatuses(adapterStatuses);
        
        return summary;
    }
    
    /**
     * 指标摘要类
     */
    public static class MetricsSummary {
        private Long tasksSubmitted;
        private Long tasksCompleted;
        private Long tasksFailed;
        private Double taskExecutionTimeAvg;
        private Double taskExecutionTimeMax;
        private Double taskExecutionTime95th;
        private Integer globalQueueSize;
        private Integer delayQueueSize;
        private Map<String, Integer> adapterQueueSizes;
        private Map<String, AdapterStatus> adapterStatuses;
        
        // getters和setters...
    }
}
```

性能指标收集机制考虑了以下关键点：

- **多维度指标**：收集任务、队列、适配器等多个维度的指标，全面反映系统状态。
- **实时更新**：定期更新指标，确保监控数据的及时性。
- **标签分类**：使用标签对指标进行分类，便于查询和分析。
- **统计计算**：计算平均值、最大值、百分位数等统计值，反映系统性能特征。

**2. 健康检查**

健康检查是监控系统健康状态的重要手段，我们实现了多层次的健康检查机制，及时发现系统问题。

```java
/**
 * 调度层健康检查器
 */
public class SchedulerHealthChecker {
    private final TaskQueueManager queueManager;
    private final AdapterRegistry adapterRegistry;
    private final TaskRepository taskRepository;
    private final RedisConnectionFactory redisConnectionFactory;
    private final DataSource dataSource;
    private final ScheduledExecutorService scheduler;
    private final Map<String, HealthStatus> componentStatuses = new ConcurrentHashMap<>();
    private final Logger logger = LoggerFactory.getLogger(SchedulerHealthChecker.class);
    
    public SchedulerHealthChecker(TaskQueueManager queueManager, AdapterRegistry adapterRegistry,
                                 TaskRepository taskRepository, RedisConnectionFactory redisConnectionFactory,
                                 DataSource dataSource) {
        this.queueManager = queueManager;
        this.adapterRegistry = adapterRegistry;
        this.taskRepository = taskRepository;
        this.redisConnectionFactory = redisConnectionFactory;
        this.dataSource = dataSource;
        this.scheduler = Executors.newSingleThreadScheduledExecutor();
        
        // 初始化组件状态
        componentStatuses.put("queue", HealthStatus.UNKNOWN);
        componentStatuses.put("adapter", HealthStatus.UNKNOWN);
        componentStatuses.put("database", HealthStatus.UNKNOWN);
        componentStatuses.put("redis", HealthStatus.UNKNOWN);
    }
    
    /**
     * 启动健康检查
     */
    public void start() {
        logger.info("Starting scheduler health checker");
        
        // 定期执行健康检查
        scheduler.scheduleAtFixedDelay(this::checkHealth, 0, 30, TimeUnit.SECONDS);
    }
    
    /**
     * 停止健康检查
     */
    public void stop() {
        logger.info("Stopping scheduler health checker");
        
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            scheduler.shutdownNow();
        }
    }
    
    /**
     * 执行健康检查
     */
    private void checkHealth() {
        try {
            // 检查队列健康状态
            checkQueueHealth();
            
            // 检查适配器健康状态
            checkAdapterHealth();
            
            // 检查数据库健康状态
            checkDatabaseHealth();
            
            // 检查Redis健康状态
            checkRedisHealth();
            
            // 记录整体健康状态
            logOverallHealth();
        } catch (Exception e) {
            logger.error("Error in health check", e);
        }
    }
    
    /**
     * 检查队列健康状态
     */
    private void checkQueueHealth() {
        try {
            // 获取队列统计信息
            QueueStats queueStats = queueManager.getQueueStats();
            
            // 检查队列大小是否正常
            boolean isHealthy = queueStats.getGlobalQueueSize() < 1000 && queueStats.getDelayQueueSize() < 1000;
            
            // 更新健康状态
            componentStatuses.put("queue", isHealthy ? HealthStatus.UP : HealthStatus.DEGRADED);
            
            logger.debug("Queue health check: {}", componentStatuses.get("queue"));
        } catch (Exception e) {
            logger.error("Queue health check failed", e);
            componentStatuses.put("queue", HealthStatus.DOWN);
        }
    }
    
    /**
     * 检查适配器健康状态
     */
    private void checkAdapterHealth() {
        try {
            // 获取所有适配器状态
            List<String> adapterIds = adapterRegistry.getAllAdapterIds();
            int availableCount = 0;
            
            for (String adapterId : adapterIds) {
                if (adapterRegistry.getAdapterStatus(adapterId) == AdapterStatus.AVAILABLE) {
                    availableCount++;
                }
            }
            
            // 检查可用适配器比例
            if (adapterIds.isEmpty()) {
                componentStatuses.put("adapter", HealthStatus.DOWN);
            } else {
                double availableRatio = (double) availableCount / adapterIds.size();
                if (availableRatio >= 0.8) {
                    componentStatuses.put("adapter", HealthStatus.UP);
                } else if (availableRatio >= 0.5) {
                    componentStatuses.put("adapter", HealthStatus.DEGRADED);
                } else {
                    componentStatuses.put("adapter", HealthStatus.DOWN);
                }
            }
            
            logger.debug("Adapter health check: {}", componentStatuses.get("adapter"));
        } catch (Exception e) {
            logger.error("Adapter health check failed", e);
            componentStatuses.put("adapter", HealthStatus.DOWN);
        }
    }
    
    /**
     * 检查数据库健康状态
     */
    private void checkDatabaseHealth() {
        try (Connection conn = dataSource.getConnection();
             Statement stmt = conn.createStatement()) {
            
            // 执行简单查询
            ResultSet rs = stmt.executeQuery("SELECT 1");
            boolean isHealthy = rs.next() && rs.getInt(1) == 1;
            
            // 更新健康状态
            componentStatuses.put("database", isHealthy ? HealthStatus.UP : HealthStatus.DOWN);
            
            logger.debug("Database health check: {}", componentStatuses.get("database"));
        } catch (Exception e) {
            logger.error("Database health check failed", e);
            componentStatuses.put("database", HealthStatus.DOWN);
        }
    }
    
    /**
     * 检查Redis健康状态
     */
    private void checkRedisHealth() {
        try {
            RedisConnection conn = redisConnectionFactory.getConnection();
            boolean isHealthy = conn.ping() != null;
            conn.close();
            
            // 更新健康状态
            componentStatuses.put("redis", isHealthy ? HealthStatus.UP : HealthStatus.DOWN);
            
            logger.debug("Redis health check: {}", componentStatuses.get("redis"));
        } catch (Exception e) {
            logger.error("Redis health check failed", e);
            componentStatuses.put("redis", HealthStatus.DOWN);
        }
    }
    
    /**
     * 记录整体健康状态
     */
    private void logOverallHealth() {
        // 计算整体健康状态
        HealthStatus overallStatus = calculateOverallHealth();
        
        // 记录健康状态
        if (overallStatus == HealthStatus.DOWN) {
            logger.error("Scheduler health status: DOWN");
        } else if (overallStatus == HealthStatus.DEGRADED) {
            logger.warn("Scheduler health status: DEGRADED");
        } else {
            logger.info("Scheduler health status: UP");
        }
    }
    
    /**
     * 计算整体健康状态
     */
    private HealthStatus calculateOverallHealth() {
        // 如果任何关键组件DOWN，则整体DOWN
        if (componentStatuses.get("database") == HealthStatus.DOWN || 
            componentStatuses.get("redis") == HealthStatus.DOWN) {
            return HealthStatus.DOWN;
        }
        
        // 如果任何组件DEGRADED，则整体DEGRADED
        if (componentStatuses.values().contains(HealthStatus.DEGRADED)) {
            return HealthStatus.DEGRADED;
        }
        
        // 如果所有组件UP，则整体UP
        if (componentStatuses.values().stream().allMatch(status -> status == HealthStatus.UP)) {
            return HealthStatus.UP;
        }
        
        // 其他情况，返回UNKNOWN
        return HealthStatus.UNKNOWN;
    }
    
    /**
     * 获取健康状态
     */
    public HealthStatus getHealthStatus() {
        return calculateOverallHealth();
    }
    
    /**
     * 获取组件健康状态
     */
    public Map<String, HealthStatus> getComponentStatuses() {
        return new HashMap<>(componentStatuses);
    }
    
    /**
     * 获取详细健康信息
     */
    public HealthInfo getHealthInfo() {
        HealthInfo info = new HealthInfo();
        info.setStatus(calculateOverallHealth());
        info.setComponents(new HashMap<>(componentStatuses));
        info.setTimestamp(System.currentTimeMillis());
        
        // 添加详细信息
        Map<String, Object> details = new HashMap<>();
        
        // 队列详情
        QueueStats queueStats = queueManager.getQueueStats();
        details.put("queueSizes", Map.of(
            "global", queueStats.getGlobalQueueSize(),
            "delay", queueStats.getDelayQueueSize()
        ));
        
        // 适配器详情
        Map<String, String> adapterStatuses = new HashMap<>();
        for (String adapterId : adapterRegistry.getAllAdapterIds()) {
            adapterStatuses.put(adapterId, adapterRegistry.getAdapterStatus(adapterId).name());
        }
        details.put("adapterStatuses", adapterStatuses);
        
        info.setDetails(details);
        
        return info;
    }
    
    /**
     * 健康状态枚举
     */
    public enum HealthStatus {
        UP,         // 健康
        DOWN,       // 不可用
        DEGRADED,   // 性能下降
        UNKNOWN     // 未知状态
    }
    
    /**
     * 健康信息类
     */
    public static class HealthInfo {
        private HealthStatus status;
        private Map<String, HealthStatus> components;
        private Map<String, Object> details;
        private long timestamp;
        
        // getters和setters...
    }
}
```

健康检查机制考虑了以下关键点：

- **多组件检查**：检查队列、适配器、数据库、Redis等多个组件的健康状态，全面反映系统健康状况。
- **状态分级**：将健康状态分为UP、DEGRADED、DOWN等级别，反映不同的健康程度。
- **定期检查**：定期执行健康检查，及时发现系统问题。
- **详细信息**：提供详细的健康信息，便于问题诊断和分析。
