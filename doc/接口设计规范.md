# 接口设计规范

## 1. 概述

本规范旨在为Java开发团队提供统一的RESTful API设计标准，确保接口的一致性、可维护性和可扩展性。本规范基于RESTful架构风格，结合项目实际需求制定。

### 1.1 RESTful架构优势

1. **URL具有强可读性和自描述性**
2. **规范化请求过程和返回结果**
3. **资源描述与视图的松耦合**
4. **可提供OpenAPI，便于第三方系统集成，提高互操作性**
5. **提供无状态的服务接口，降低复杂度，可提高应用的水平扩展性**

## 2. 版本管理

### 2.1 版本号规范

- **PC端版本号**：以`pc`开始
- **移动端版本号**：以`mobile`开始
- **服务端版本号**：以`service`开始

### 2.2 版本号格式

```
/api/{platform}/v{version}/{resources}
```

**示例：**
```
GET /api/pc/v1/users/{user-id}     // PC端版本v1的查询用户API接口
GET /api/pc/v2/users/{user-id}     // PC端版本v2的查询用户API接口
GET /api/mobile/v1/users/{user-id} // 移动端版本v1的查询用户API接口
```

### 2.3 版本兼容性

- 命名版本号可以解决版本不兼容问题
- 在URL中保留旧版本号，同时兼容多个版本
- 新版本发布时，旧版本应保持一定时间的向后兼容

## 3. 资源路径设计

### 3.1 基本原则

- **【强制】** 禁止使用PUT、DELETE、PATCH方法，只允许使用GET和POST方法
- **【强制】** 禁止使用路径参数，所有参数通过查询参数或请求体传递
- 资源路径应该从根到子依次组织
- URL路径不能使用大写，单词分隔统一使用中划线
- 路径禁止携带表示请求内容类型的后缀（如.json、.xml）

### 3.2 路径结构

```
/{resources}/{sub-resources}/{sub-resource-property}
```

**示例：**
```
POST /api/pc/v1/users/roles        // 添加用户角色
GET  /api/pc/v1/users/profiles     // 查询用户档案
POST /api/pc/v1/users/password/update  // 密码修改
POST /api/pc/v1/user-groups/members     // 用户组成员管理
GET  /api/pc/v1/order-items/details     // 订单项详情
```

### 3.3 特殊操作命名

当资源变化难以使用标准RESTful API命名时，可以考虑使用特殊的actions：

- `add` - 添加操作
- `delete` - 删除操作  
- `find` - 查询操作
- `update` - 更新操作

**示例：**
```
POST /api/pc/v1/users/find         // 复杂查询用户
POST /api/pc/v1/users/delete       // 删除用户
POST /api/pc/v1/users/update       // 更新用户
POST /api/pc/v1/user-groups/find   // 查询用户组
POST /api/pc/v1/order-items/update // 更新订单项
```

## 4. 请求方法规范

### 4.1 HTTP方法使用

**【强制】** 只允许使用GET和POST方法：

| 方法 | 用途 | 示例 | 描述 |
|------|------|------|------|
| GET | 查询操作 | `/api/pc/v1/users` | 查询用户列表（参数≤5个） |
| GET | 查看详情 | `/api/pc/v1/users?id=1001` | 查看某个用户信息 |
| POST | 复杂查询 | `/api/pc/v1/users/find` | 查询用户列表（参数>5个） |
| POST | 删除操作 | `/api/pc/v1/users/delete` | 删除用户 |
| POST | 更新操作 | `/api/pc/v1/users/update` | 修改用户信息 |
| POST | 创建操作 | `/api/pc/v1/users/add` | 创建新用户 |

### 4.2 参数传递规则

- **GET请求**：参数个数≤5个时使用GET请求，参数通过URL查询字符串传递
- **POST请求**：参数个数>5个时使用POST请求，参数通过请求体传递

## 5. 统一前缀

**【强制】** 所有接口都以`/api`开头，便于统一处理和管理。

```
/api/{platform}/v{version}/{resources}
```

## 6. 分页与排序

### 6.1 分页参数

在URL中增加以下参数用于分页：

- `page-size` - 每页记录数
- `page-num` - 页码（从1开始）

**示例：**
```
GET /api/pc/v1/users?page-size=20&page-num=1
```

### 6.2 排序参数

在URL中增加`sort`参数用于排序：

- 默认正序
- 带`-`表示倒序
- 多字段排序用逗号分隔

**示例：**
```
GET /api/pc/v1/users?sort=-create-time,user-name
// 表示按create-time倒序，user-name正序
```

### 6.3 分页响应格式

返回的分页数据中，响应体包含以下元素：

```json
{
    "code": 0,
    "msg": "",
    "data": {
        "data": [...],           // 当前页数据
        "currentPage": 1,        // 当前页码
        "pageSize": 20,          // 每页记录数
        "totalCount": 100,       // 总记录数（可选，以提高性能）
        "totalPage": 5           // 总页数
    }
}
```

## 7. 请求体规范

### 7.1 Content-Type要求

- **文件上传**：采用`multipart/form-data`格式
- **其他请求**：均采用`application/json`格式
- **【强制】** 不得使用`application/x-www-form-urlencoded`

### 7.2 请求体示例

```json
{
    "userName": "zhangsan",
    "userAge": 25,
    "userEmail": "<EMAIL>",
    "userGroup": "admin-group",
    "createTime": "2024-03-20 10:30:00"
}
```

## 8. 响应规范

### 8.1 响应状态码

使用标准HTTP状态码，如无必要，勿增实体：

#### 2XX 成功
- **200** - 请求成功，响应体中带有资源的数据
- **201** - 资源创建成功
- **202** - 请求已接收（用于异步处理）
- **204** - 响应中无内容

#### 3XX 重定向
- **301** - 接口废弃，迁移到新接口
- **304** - 资源未修改

#### 4XX 客户端错误
- **400** - 错误请求，通常是参数不正确
- **401** - 未授权，客户端未提供有效认证信息
- **403** - 请求被拒绝，无权访问该资源
- **404** - 资源不存在
- **410** - 接口永久废弃（慎用）
- **429** - 请求次数超限

#### 5XX 服务器错误
- **500** - 服务器内部错误

### 8.2 响应头规范

- **Content-Type**：需要与数据保持一致
- **POJO类数据**：统一采用`application/json;charset=UTF-8`

### 8.3 响应体格式

**【强制】** 除文件下载外，响应体均采用JSON格式，JSON不要进行格式化：

```json
{
    "code": 0,        // 状态码：0表示成功，其他表示失败
    "data": {},       // 响应数据，任意类型
    "msg": ""         // 错误信息，成功时为空
}
```

### 8.4 错误响应示例

```json
{
    "code": 50003,
    "data": null,
    "msg": "参数有误：用户名不能为空"
}
```

## 9. 状态码定义

### 9.1 用户交互状态码

| 序号 | ENUM | code | msg |
|------|------|------|-----|
| 1 | SUCCESS | 0 | 请求成功 |
| 2 | REGISTER | 10001 | 注册成功 |
| 3 | ALREADY_LOGIN | 10002 | 已登录 |
| 4 | NO_LOGIN | 10003 | 未登录 |
| 5 | NO_APPROVED | 10004 | 未通过审核 |
| 6 | APPROVED | 10005 | 已通过审核 |
| 7 | ACCOUNT_PWD_NOT_EXIST | 10006 | 用户名或密码不存在 |
| 8 | ACCOUNT_EXIST | 10007 | 账号已存在 |
| 9 | NO_PERMISSION | 10008 | 无访问权限 |

### 9.2 前后端状态码

| 序号 | ENUM | code | msg |
|------|------|------|-----|
| 1 | SESSION_TIME_OUT | 50001 | 会话超时 |
| 2 | TOKEN_ERROR | 50002 | token不合法 |
| 3 | PARAMS_ERROR | 50003 | 参数有误 |
| 4 | PARAM_INCOMPLETE | 50004 | 参数不完整 |
| 5 | PARAM_ILLEGAL | 50005 | 参数不合法 |
| 6 | DATABASE_ERROR | 50006 | 数据库连接异常 |
| 7 | MONITOR_ERROR | 50007 | 文件监控异常 |
| 8 | SERVER_ERROR | 50008 | 服务端错误 |
| 9 | UNKNOWN_ERROR | 50009 | 未知错误 |
| 10 | NO_DATA | 50010 | 无数据 |
| 11 | DATA_EXISTS | 50011 | 记录已存在 |
| 12 | DELETE_ERROR | 50012 | 删除错误 |
| 13 | UPDATE_ERROR | 50013 | 修改错误 |
| 14 | SYS_ERROR | 50014 | 系统错误，请重试 |
| 15 | SERVICE_INVOKE_FAIL | 50015 | 服务器调用失败 |

## 10. 数据字典规范

### 10.1 码表返回格式

```json
{
    "label": "显示名称",
    "value": "实际值"
}
```

### 10.2 数据库设计

**字典类型表（sys-dict-type）：**
```sql
CREATE TABLE `sys_dict_type` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `NAME_EN` varchar(255) DEFAULT NULL COMMENT '英文名称',
  `NAME_CN` varchar(255) DEFAULT NULL COMMENT '中文名称',
  `STATUS` tinyint(4) DEFAULT '1',
  `SYSTEM_ID` tinyint(4) DEFAULT '1' COMMENT '1.卖家 2.买家 3.运营系统',
  `CREATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
```

**字典数据表（sys-dict）：**
```sql
CREATE TABLE `sys_dict` (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `LABEL` varchar(255) DEFAULT NULL COMMENT '字典数据名称',
  `DICT_TYPE_ID` bigint(20) DEFAULT NULL COMMENT '字典类型ID',
  `VALUE` tinyint(4) DEFAULT NULL COMMENT '字典数据值',
  `SORT` int(255) DEFAULT NULL COMMENT '排序',
  `STATUS` tinyint(4) DEFAULT '1',
  `CREATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP,
  `UPDATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
```

## 11. 接口安全要求

### 11.1 认证授权

- **【强制】** 接口需要做认证
- **【强制】** 接口需要无状态，不能依赖于会话（session）
- 使用Token机制进行身份验证
- 实现基于角色的访问控制（RBAC）

### 11.2 数据安全

- 敏感信息不能在URL中传递
- 使用HTTPS协议（生产环境强制）
- 对敏感数据进行加密处理

## 12. 开发注意事项

### 12.1 命名规范

**【强制】** 在前后端交互的JSON格式数据中，所有的key必须为小写字母开始的lowerCamelCase风格：

**正例：**
```json
{
    "errorCode": 50003,
    "errorMessage": "参数有误",
    "assetStatus": 1,
    "menuList": [],
    "orderList": [],
    "configFlag": true,
    "userGroup": "admin-group",
    "createTime": "2024-03-20 10:30:00"
}
```

**反例：**
```json
{
    "ERRORCODE": 50003,
    "ERROR_CODE": 50003,
    "error_message": "参数有误",
    "error-message": "参数有误",
    "user_group": "admin-group"
}
```

### 12.2 数据类型处理

**【强制】** 对于需要使用超大整数的场景，服务端一律使用String字符串类型返回，禁止使用Long类型。

**说明：** Long类型超过2^53次方的数值转化为JS的Number时会有精度损失。

### 12.3 URL长度限制

**【强制】** HTTP请求通过URL传递参数时，不能超过2048字节。

### 12.4 请求体大小限制

**【强制】** HTTP请求通过body传递内容时，必须控制长度：
- nginx默认限制：1MB
- tomcat默认限制：2MB

### 12.5 分页处理

**【强制】** 在翻页场景中：
- 用户输入参数小于1，前端返回第一页参数给后端
- 后端发现用户输入参数大于总页数，直接返回最后一页

### 12.6 时间格式

**【推荐】** 前后端的时间格式统一为`yyyy-MM-dd HH:mm:ss`，统一为GMT。

### 12.7 数据返回

**【强制】** 前后端数据列表相关的接口返回，如果为空，则返回空数组`[]`或空集合`{}`。

## 13. 示例代码

### 13.1 Controller示例

```java
@RestController
@RequestMapping("/api/pc/v1")
@Api(tags = "用户相关接口")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 查询用户列表（简单查询）
     */
    @GetMapping("/users")
    @ApiOperation("查询用户列表")
    public R<List<User>> getUserList(
            @RequestParam(required = false) String keyword,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "20") Integer pageSize) {
        return R.ok(userService.getUserList(keyword, pageNum, pageSize));
    }

    /**
     * 查询用户列表（复杂查询）
     */
    @PostMapping("/users/find")
    @ApiOperation("复杂查询用户列表")
    public R<PageResult<User>> findUsers(@RequestBody UserQueryRequest request) {
        return R.ok(userService.findUsers(request));
    }

    /**
     * 查看用户详情
     */
    @GetMapping("/users/detail")
    @ApiOperation("查看用户详情")
    public R<User> getUserDetail(@RequestParam Long userId) {
        return R.ok(userService.getUserById(userId));
    }

    /**
     * 添加用户
     */
    @PostMapping("/users/add")
    @ApiOperation("添加用户")
    public R<User> addUser(@RequestBody @Valid UserAddRequest request) {
        return R.ok(userService.addUser(request));
    }

    /**
     * 更新用户
     */
    @PostMapping("/users/update")
    @ApiOperation("更新用户")
    public R<User> updateUser(@RequestBody @Valid UserUpdateRequest request) {
        return R.ok(userService.updateUser(request));
    }

    /**
     * 删除用户
     */
    @PostMapping("/users/delete")
    @ApiOperation("删除用户")
    public R<Void> deleteUser(@RequestBody UserDeleteRequest request) {
        userService.deleteUser(request.getUserId());
        return R.ok();
    }

    /**
     * 修改密码
     */
    @PostMapping("/users/password/update")
    @ApiOperation("修改密码")
    public R<Void> updatePassword(@RequestBody @Valid PasswordUpdateRequest request) {
        userService.updatePassword(request);
        return R.ok();
    }

    /**
     * 查询用户组
     */
    @PostMapping("/user-groups/find")
    @ApiOperation("查询用户组")
    public R<PageResult<UserGroup>> findUserGroups(@RequestBody UserGroupQueryRequest request) {
        return R.ok(userService.findUserGroups(request));
    }

    /**
     * 管理用户组成员
     */
    @PostMapping("/user-groups/members/update")
    @ApiOperation("更新用户组成员")
    public R<Void> updateGroupMembers(@RequestBody GroupMemberUpdateRequest request) {
        userService.updateGroupMembers(request);
        return R.ok();
    }
}
```

### 13.2 统一返回结果类

```java
public class R<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 成功 */
    public static final int SUCCESS = 0;
    /** 失败 */
    public static final int FAIL = -1;

    private int code;
    private String msg;
    private T data;

    public static <T> R<T> ok() {
        return restResult(null, SUCCESS, "操作成功");
    }

    public static <T> R<T> ok(T data) {
        return restResult(data, SUCCESS, "操作成功");
    }

    public static <T> R<T> ok(T data, String msg) {
        return restResult(data, SUCCESS, msg);
    }

    public static <T> R<T> fail() {
        return restResult(null, FAIL, "操作失败");
    }

    public static <T> R<T> fail(String msg) {
        return restResult(null, FAIL, msg);
    }

    public static <T> R<T> fail(int code, String msg) {
        return restResult(null, code, msg);
    }

    private static <T> R<T> restResult(T data, int code, String msg) {
        R<T> apiResult = new R<>();
        apiResult.setCode(code);
        apiResult.setData(data);
        apiResult.setMsg(msg);
        return apiResult;
    }

    // getter and setter methods...
}
```

### 13.3 分页结果类

```java
public class PageResult<T> {
    private List<T> data;
    private Integer currentPage;
    private Integer pageSize;
    private Long totalCount;
    private Integer totalPage;

    // constructors, getter and setter methods...
}
```

### 13.4 请求参数示例

```java
// 用户查询请求
public class UserQueryRequest {
    private String userName;
    private String userEmail;
    private String userGroup;
    private String createTimeStart;
    private String createTimeEnd;
    private Integer pageNum;
    private Integer pageSize;
    private String sortField;
    private String sortOrder;
    
    // getter and setter methods...
}

// 用户组查询请求
public class UserGroupQueryRequest {
    private String groupName;
    private String groupType;
    private String createTimeStart;
    private String createTimeEnd;
    private Integer pageNum;
    private Integer pageSize;
    
    // getter and setter methods...
}
```

## 14. 接口文档

### 14.1 文档要求

**【强制】** 前后端交互的API，需要明确以下内容：
1. 协议（生产环境必须使用HTTPS）
2. 域名
3. 路径
4. 请求方法
5. 请求内容
6. 状态码
7. 响应体

### 14.2 文档工具

推荐使用以下工具生成API文档：
- Swagger/OpenAPI 3.0
- Postman
- Apifox

### 14.3 接口文档示例

```yaml
# OpenAPI 3.0 示例
paths:
  /api/pc/v1/users/find:
    post:
      tags:
        - 用户管理
      summary: 复杂查询用户列表
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserQueryRequest'
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserListResponse'
```

## 15. 测试规范

### 15.1 接口测试

- 单元测试覆盖率不低于80%
- 集成测试覆盖主要业务流程
- 性能测试验证接口响应时间

### 15.2 测试用例

每个接口至少包含以下测试用例：
- 正常场景测试
- 异常参数测试
- 边界值测试
- 权限验证测试

### 15.3 测试数据

```java
@Test
public void testFindUsers() {
    UserQueryRequest request = new UserQueryRequest();
    request.setUserName("test-user");
    request.setUserGroup("admin-group");
    request.setPageNum(1);
    request.setPageSize(20);
    
    R<PageResult<User>> response = userController.findUsers(request);
    
    assertEquals(0, response.getCode());
    assertNotNull(response.getData());
}
```

## 16. 监控与日志

### 16.1 接口监控

- 响应时间监控
- 错误率监控
- 调用量监控
- 可用性监控

### 16.2 日志规范

- 记录请求和响应信息
- 记录异常堆栈信息
- 敏感信息脱敏处理
- 日志级别合理设置

### 16.3 日志示例

```java
@Slf4j
@RestController
public class UserController {
    
    @PostMapping("/users/find")
    public R<PageResult<User>> findUsers(@RequestBody UserQueryRequest request) {
        log.info("查询用户列表，请求参数：{}", JSON.toJSONString(request));
        
        try {
            PageResult<User> result = userService.findUsers(request);
            log.info("查询用户列表成功，返回{}条记录", result.getTotalCount());
            return R.ok(result);
        } catch (Exception e) {
            log.error("查询用户列表失败", e);
            return R.fail("查询失败");
        }
    }
}
```

## 17. 版本升级策略

### 17.1 向后兼容

- 新增字段不影响旧版本
- 废弃字段保留一定时间
- 提供迁移指南

### 17.2 版本发布

- 遵循语义化版本控制
- 提前通知接口变更
- 提供过渡期支持

### 17.3 版本升级示例

```
// v1版本
GET /api/pc/v1/users?user-name=zhangsan

// v2版本（新增字段）
GET /api/pc/v2/users?user-name=zhangsan&user-status=active

// 响应格式保持兼容
{
    "code": 0,
    "data": {
        "userName": "zhangsan",
        "userEmail": "<EMAIL>",
        "userStatus": "active"  // v2新增字段
    },
    "msg": ""
}
```

## 18. 常见问题与解决方案

### 18.1 URL命名冲突

**问题：** 多个单词组合时的命名规范
**解决方案：** 统一使用中划线分隔

```
// 正确
/api/pc/v1/user-groups
/api/pc/v1/order-items
/api/pc/v1/product-categories

// 错误
/api/pc/v1/user_groups
/api/pc/v1/userGroups
/api/pc/v1/UserGroups
```

### 18.2 参数传递方式选择

**问题：** 何时使用GET，何时使用POST
**解决方案：** 
- 参数≤5个且为简单查询：使用GET
- 参数>5个或包含复杂条件：使用POST
- 所有修改操作：使用POST

### 18.3 错误处理统一

**问题：** 错误信息不统一
**解决方案：** 使用统一的错误码和错误信息格式

```java
// 统一异常处理
@ControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(ValidationException.class)
    public R<Void> handleValidationException(ValidationException e) {
        return R.fail(ErrorCode.PARAMS_ERROR.getCode(), e.getMessage());
    }
}
```

---

**本规范自发布之日起生效，所有新开发的接口必须严格遵循本规范。对于已有接口，应在版本升级时逐步改造以符合本规范要求。**
