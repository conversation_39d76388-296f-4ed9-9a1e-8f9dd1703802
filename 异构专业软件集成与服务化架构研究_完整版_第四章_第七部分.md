# 面向异构专业软件的服务化集成架构研究（续）

#### 4.2.3 性能优化技术

调度层作为系统的核心控制中心，其性能直接影响整个系统的响应速度和吞吐量。我们采用了多种性能优化技术，提高调度层的处理能力。

**1. 任务批处理**

为了减少系统开销，我们实现了任务批处理机制，将多个小任务合并为批量操作，提高处理效率。

```java
/**
 * 任务批处理器
 */
public class TaskBatchProcessor {
    private final TaskExecutor taskExecutor;
    private final TaskRepository taskRepository;
    private final ApplicationEventPublisher eventPublisher;
    private final int batchSize;
    private final long maxWaitTimeMs;
    private final BlockingQueue<Task> taskQueue;
    private final ScheduledExecutorService scheduler;
    private final AtomicBoolean processing = new AtomicBoolean(false);
    private final Logger logger = LoggerFactory.getLogger(TaskBatchProcessor.class);
    
    public TaskBatchProcessor(TaskExecutor taskExecutor, TaskRepository taskRepository,
                             ApplicationEventPublisher eventPublisher, int batchSize, long maxWaitTimeMs) {
        this.taskExecutor = taskExecutor;
        this.taskRepository = taskRepository;
        this.eventPublisher = eventPublisher;
        this.batchSize = batchSize;
        this.maxWaitTimeMs = maxWaitTimeMs;
        this.taskQueue = new LinkedBlockingQueue<>();
        this.scheduler = Executors.newSingleThreadScheduledExecutor();
    }
    
    /**
     * 启动批处理器
     */
    public void start() {
        logger.info("Starting task batch processor");
        
        // 定期检查是否需要处理批次
        scheduler.scheduleWithFixedDelay(this::checkAndProcessBatch, 0, maxWaitTimeMs / 2, TimeUnit.MILLISECONDS);
    }
    
    /**
     * 停止批处理器
     */
    public void stop() {
        logger.info("Stopping task batch processor");
        
        scheduler.shutdown();
        try {
            // 处理剩余任务
            processTaskBatch();
            
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            scheduler.shutdownNow();
        }
    }
    
    /**
     * 提交任务到批处理器
     */
    public void submitTask(Task task) {
        taskQueue.offer(task);
        
        // 如果队列达到批处理大小，立即处理
        if (taskQueue.size() >= batchSize) {
            checkAndProcessBatch();
        }
    }
    
    /**
     * 检查并处理批次
     */
    private void checkAndProcessBatch() {
        // 如果已经在处理中，或者队列为空，则跳过
        if (processing.get() || taskQueue.isEmpty()) {
            return;
        }
        
        // 如果队列达到批处理大小，或者最早的任务等待时间超过最大等待时间，则处理批次
        if (taskQueue.size() >= batchSize || isWaitTimeExceeded()) {
            processTaskBatch();
        }
    }
    
    /**
     * 检查等待时间是否超过最大等待时间
     */
    private boolean isWaitTimeExceeded() {
        Task oldestTask = taskQueue.peek();
        if (oldestTask == null) {
            return false;
        }
        
        long waitTime = System.currentTimeMillis() - oldestTask.getCreateTime();
        return waitTime >= maxWaitTimeMs;
    }
    
    /**
     * 处理任务批次
     */
    private void processTaskBatch() {
        // 如果已经在处理中，则跳过
        if (!processing.compareAndSet(false, true)) {
            return;
        }
        
        try {
            // 收集批次任务
            List<Task> batchTasks = new ArrayList<>();
            Task task;
            int count = 0;
            
            while ((task = taskQueue.poll()) != null && count < batchSize) {
                batchTasks.add(task);
                count++;
            }
            
            if (batchTasks.isEmpty()) {
                return;
            }
            
            logger.debug("Processing batch of {} tasks", batchTasks.size());
            
            // 按类型分组任务
            Map<String, List<Task>> tasksByType = batchTasks.stream()
                    .collect(Collectors.groupingBy(t -> t.getAdapterId() + ":" + t.getOperationName()));
            
            // 处理每组任务
            for (Map.Entry<String, List<Task>> entry : tasksByType.entrySet()) {
                processBatchGroup(entry.getKey(), entry.getValue());
            }
        } finally {
            processing.set(false);
        }
    }
    
    /**
     * 处理批次任务组
     */
    private void processBatchGroup(String groupKey, List<Task> tasks) {
        if (tasks.isEmpty()) {
            return;
        }
        
        logger.debug("Processing batch group: {}, tasks: {}", groupKey, tasks.size());
        
        try {
            // 创建批处理任务
            Task batchTask = createBatchTask(tasks);
            
            // 执行批处理任务
            Result batchResult = taskExecutor.executeTask(batchTask);
            
            // 处理批处理结果
            handleBatchResult(tasks, batchResult);
        } catch (Exception e) {
            logger.error("Failed to process batch group: {}", groupKey, e);
            
            // 处理批处理异常
            handleBatchException(tasks, e);
        }
    }
    
    /**
     * 创建批处理任务
     */
    private Task createBatchTask(List<Task> tasks) {
        Task firstTask = tasks.get(0);
        
        // 创建批处理任务
        Task batchTask = Task.builder()
                .name("Batch: " + firstTask.getName())
                .description("Batch processing of " + tasks.size() + " tasks")
                .adapterId(firstTask.getAdapterId())
                .operationName(firstTask.getOperationName())
                .param("batchTasks", tasks)
                .param("batchSize", tasks.size())
                .priority(firstTask.getPriority())
                .build();
        
        return batchTask;
    }
    
    /**
     * 处理批处理结果
     */
    private void handleBatchResult(List<Task> tasks, Result batchResult) {
        if (batchResult.isSuccess()) {
            // 批处理成功
            @SuppressWarnings("unchecked")
            List<Result> individualResults = (List<Result>) batchResult.getData().get("results");
            
            // 处理每个任务的结果
            for (int i = 0; i < tasks.size(); i++) {
                Task task = tasks.get(i);
                Result result = i < individualResults.size() ? individualResults.get(i) : Result.failed("No result");
                
                // 更新任务状态和结果
                task.setEndTime(System.currentTimeMillis());
                task.setResult(result);
                
                if (result.isSuccess()) {
                    // 任务成功
                    task.setStatus(TaskStatus.COMPLETED);
                    taskRepository.save(task);
                    
                    // 发布任务完成事件
                    publishTaskEvent(new TaskEvent(this, TaskEventType.COMPLETED, task));
                } else {
                    // 任务失败
                    task.setStatus(TaskStatus.FAILED);
                    task.setErrorMessage(result.getMessage());
                    taskRepository.save(task);
                    
                    // 发布任务失败事件
                    publishTaskEvent(new TaskEvent(this, TaskEventType.FAILED, task));
                }
            }
        } else {
            // 批处理失败
            handleBatchException(tasks, new RuntimeException(batchResult.getMessage()));
        }
    }
    
    /**
     * 处理批处理异常
     */
    private void handleBatchException(List<Task> tasks, Exception e) {
        // 将所有任务标记为失败
        for (Task task : tasks) {
            task.setEndTime(System.currentTimeMillis());
            task.setStatus(TaskStatus.FAILED);
            task.setErrorMessage("Batch processing failed: " + e.getMessage());
            taskRepository.save(task);
            
            // 发布任务失败事件
            publishTaskEvent(new TaskEvent(this, TaskEventType.FAILED, task));
        }
    }
    
    /**
     * 发布任务事件
     */
    private void publishTaskEvent(TaskEvent event) {
        eventPublisher.publishEvent(event);
    }
}
```

任务批处理机制考虑了以下关键点：

- **批次大小**：根据任务特性设置合适的批次大小，平衡处理效率和响应时间。
- **等待时间**：设置最大等待时间，避免任务长时间等待。
- **分组处理**：按任务类型分组，确保批处理的任务具有相似特性。
- **结果处理**：将批处理结果映射回各个任务，确保每个任务都有正确的结果。

**2. 异步处理**

为了提高系统的并发处理能力，我们采用异步处理模式，将任务提交和执行分离，避免阻塞。

```java
/**
 * 异步任务处理器
 */
public class AsyncTaskProcessor {
    private final TaskExecutor taskExecutor;
    private final TaskRepository taskRepository;
    private final ApplicationEventPublisher eventPublisher;
    private final ExecutorService executorService;
    private final Logger logger = LoggerFactory.getLogger(AsyncTaskProcessor.class);
    
    public AsyncTaskProcessor(TaskExecutor taskExecutor, TaskRepository taskRepository,
                             ApplicationEventPublisher eventPublisher, int threadPoolSize) {
        this.taskExecutor = taskExecutor;
        this.taskRepository = taskRepository;
        this.eventPublisher = eventPublisher;
        
        // 创建线程池
        this.executorService = new ThreadPoolExecutor(
            threadPoolSize / 2,                  // 核心线程数
            threadPoolSize,                      // 最大线程数
            60, TimeUnit.SECONDS,                // 空闲线程存活时间
            new LinkedBlockingQueue<>(1000),     // 工作队列
            new ThreadFactoryBuilder()
                .setNameFormat("task-processor-%d")
                .setDaemon(true)
                .build(),
            new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略
        );
    }
    
    /**
     * 异步处理任务
     */
    public CompletableFuture<Result> processTask(Task task) {
        logger.debug("Submitting task for async processing: {}", task.getId());
        
        // 更新任务状态
        task.setStatus(TaskStatus.RUNNING);
        task.setStartTime(System.currentTimeMillis());
        taskRepository.save(task);
        
        // 发布任务开始事件
        publishTaskEvent(new TaskEvent(this, TaskEventType.STARTED, task));
        
        // 提交到线程池异步执行
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 执行任务
                Result result = taskExecutor.executeTask(task);
                
                // 处理执行结果
                handleTaskResult(task, result);
                
                return result;
            } catch (Exception e) {
                // 处理执行异常
                handleTaskException(task, e);
                
                // 创建失败结果
                return Result.failed(e.getMessage());
            }
        }, executorService);
    }
    
    /**
     * 处理任务执行结果
     */
    private void handleTaskResult(Task task, Result result) {
        logger.debug("Handling task result: {}, success: {}", task.getId(), result.isSuccess());
        
        // 更新任务状态和结果
        task.setEndTime(System.currentTimeMillis());
        task.setResult(result);
        
        if (result.isSuccess()) {
            // 任务成功
            task.setStatus(TaskStatus.COMPLETED);
            taskRepository.save(task);
            
            // 发布任务完成事件
            publishTaskEvent(new TaskEvent(this, TaskEventType.COMPLETED, task));
        } else {
            // 任务失败
            task.setStatus(TaskStatus.FAILED);
            task.setErrorMessage(result.getMessage());
            taskRepository.save(task);
            
            // 发布任务失败事件
            publishTaskEvent(new TaskEvent(this, TaskEventType.FAILED, task));
        }
    }
    
    /**
     * 处理任务执行异常
     */
    private void handleTaskException(Task task, Exception e) {
        logger.error("Task execution failed: {}", task.getId(), e);
        
        // 更新任务状态
        task.setEndTime(System.currentTimeMillis());
        task.setStatus(TaskStatus.FAILED);
        task.setErrorMessage(e.getMessage());
        taskRepository.save(task);
        
        // 发布任务失败事件
        publishTaskEvent(new TaskEvent(this, TaskEventType.FAILED, task));
    }
    
    /**
     * 发布任务事件
     */
    private void publishTaskEvent(TaskEvent event) {
        eventPublisher.publishEvent(event);
    }
    
    /**
     * 关闭处理器
     */
    public void shutdown() {
        logger.info("Shutting down async task processor");
        
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            executorService.shutdownNow();
        }
    }
}
```

异步处理机制考虑了以下关键点：

- **线程池管理**：使用线程池控制并发度，避免资源耗尽。
- **任务提交**：将任务异步提交到线程池，避免阻塞调用者。
- **结果处理**：使用CompletableFuture处理异步结果，支持链式操作和异常处理。
- **拒绝策略**：当线程池饱和时，采用合适的拒绝策略，确保系统稳定。

**3. 缓存优化**

为了减少重复计算和数据库访问，我们实现了多级缓存机制，提高系统响应速度。

```java
/**
 * 调度层缓存管理器
 */
public class SchedulerCacheManager {
    private final Cache<String, Object> localCache;
    private final RedisTemplate<String, Object> redisTemplate;
    private final boolean distributedCacheEnabled;
    private final Logger logger = LoggerFactory.getLogger(SchedulerCacheManager.class);
    
    public SchedulerCacheManager(CacheConfig config, RedisTemplate<String, Object> redisTemplate) {
        // 创建本地缓存
        this.localCache = Caffeine.newBuilder()
            .maximumSize(config.getMaximumSize())
            .expireAfterWrite(config.getExpireAfterWrite(), TimeUnit.SECONDS)
            .recordStats()
            .build();
        
        this.redisTemplate = redisTemplate;
        this.distributedCacheEnabled = config.isDistributedCacheEnabled() && redisTemplate != null;
        
        logger.info("Created scheduler cache manager with config: {}", config);
    }
    
    /**
     * 从缓存获取值
     */
    @SuppressWarnings("unchecked")
    public <T> T get(String key, Class<T> type) {
        // 先从本地缓存获取
        Object value = localCache.getIfPresent(key);
        if (value != null) {
            logger.debug("Cache hit (local): {}", key);
            return (T) value;
        }
        
        // 如果启用了分布式缓存，从Redis获取
        if (distributedCacheEnabled) {
            try {
                value = redisTemplate.opsForValue().get(key);
                if (value != null) {
                    logger.debug("Cache hit (redis): {}", key);
                    // 更新本地缓存
                    localCache.put(key, value);
                    return (T) value;
                }
            } catch (Exception e) {
                logger.warn("Failed to get from redis cache: {}", e.getMessage());
            }
        }
        
        logger.debug("Cache miss: {}", key);
        return null;
    }
    
    /**
     * 从缓存获取值，如果不存在则计算
     */
    @SuppressWarnings("unchecked")
    public <T> T getOrCompute(String key, Class<T> type, Supplier<T> supplier, long expireSeconds) {
        // 先从缓存获取
        T value = get(key, type);
        if (value != null) {
            return value;
        }
        
        // 计算值
        value = supplier.get();
        if (value != null) {
            // 存入缓存
            put(key, value, expireSeconds);
        }
        
        return value;
    }
    
    /**
     * 将值存入缓存
     */
    public void put(String key, Object value, long expireSeconds) {
        if (value == null) {
            return;
        }
        
        // 存入本地缓存
        localCache.put(key, value);
        
        // 如果启用了分布式缓存，存入Redis
        if (distributedCacheEnabled) {
            try {
                if (expireSeconds > 0) {
                    redisTemplate.opsForValue().set(key, value, expireSeconds, TimeUnit.SECONDS);
                } else {
                    redisTemplate.opsForValue().set(key, value);
                }
            } catch (Exception e) {
                logger.warn("Failed to put to redis cache: {}", e.getMessage());
            }
        }
        
        logger.debug("Cached value for key: {}", key);
    }
    
    /**
     * 从缓存中移除值
     */
    public void remove(String key) {
        // 从本地缓存移除
        localCache.invalidate(key);
        
        // 如果启用了分布式缓存，从Redis移除
        if (distributedCacheEnabled) {
            try {
                redisTemplate.delete(key);
            } catch (Exception e) {
                logger.warn("Failed to remove from redis cache: {}", e.getMessage());
            }
        }
        
        logger.debug("Removed from cache: {}", key);
    }
    
    /**
     * 获取缓存统计信息
     */
    public CacheStats getLocalCacheStats() {
        return localCache.stats();
    }
    
    /**
     * 缓存配置类
     */
    public static class CacheConfig {
        private long maximumSize = 10000;
        private long expireAfterWrite = 300; // 默认5分钟
        private boolean distributedCacheEnabled = false;
        
        // getters和setters...
    }
}
```

缓存优化机制考虑了以下关键点：

- **多级缓存**：结合本地缓存和分布式缓存，平衡性能和一致性。
- **缓存策略**：设置合适的缓存大小和过期时间，避免缓存过期或过大。
- **缓存穿透**：使用getOrCompute方法避免缓存穿透，确保高并发下的缓存效果。
- **缓存统计**：收集缓存使用统计信息，便于监控和优化。
