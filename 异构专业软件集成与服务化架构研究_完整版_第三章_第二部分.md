# 面向异构专业软件的服务化集成架构研究（续）

### 3.3 接口转换策略

在统一适配器模式的基础上，我们针对不同类型的接口设计了专门的转换策略，解决接口调用、数据转换和错误处理等具体问题。

#### 3.3.1 Web接口转换

Web接口是当前最常见的服务接口形式，包括RESTful API和SOAP服务。我们设计了以下转换策略：

**1. RESTful API转换**

RESTful API转换采用HTTP客户端库进行调用，将统一接口参数转换为HTTP请求参数，并将HTTP响应转换为标准结果对象。关键转换步骤包括：

- **URL构建**：根据操作名称和参数构建请求URL，支持路径参数和查询参数。
- **HTTP方法映射**：根据操作语义选择适当的HTTP方法（GET、POST、PUT、DELETE等）。
- **请求头设置**：设置Content-Type、Accept、Authorization等HTTP头信息。
- **请求体构造**：对于POST/PUT请求，将参数转换为JSON或XML格式的请求体。
- **响应解析**：解析HTTP响应状态码和响应体，转换为标准结果对象。
- **错误处理**：处理HTTP错误状态码和异常情况，转换为统一的异常模型。

实现代码示例：

```java
/**
 * RESTful API适配器
 */
public class RestApiAdapter implements SoftwareAdapter {
    private String baseUrl;
    private RestTemplate restTemplate;
    private Map<String, String> defaultHeaders;
    private boolean connected;
    
    @Override
    public boolean connect(Map<String, Object> connectionParams) throws AdapterException {
        try {
            this.baseUrl = (String) connectionParams.get("baseUrl");
            if (baseUrl == null) {
                throw new ConnectionException("Base URL is required");
            }
            
            // 创建RestTemplate实例
            this.restTemplate = new RestTemplate();
            
            // 配置默认请求头
            this.defaultHeaders = new HashMap<>();
            if (connectionParams.containsKey("headers")) {
                @SuppressWarnings("unchecked")
                Map<String, String> headers = (Map<String, String>) connectionParams.get("headers");
                this.defaultHeaders.putAll(headers);
            }
            
            // 配置认证信息
            if (connectionParams.containsKey("username") && connectionParams.containsKey("password")) {
                String username = (String) connectionParams.get("username");
                String password = (String) connectionParams.get("password");
                String auth = username + ":" + password;
                String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes());
                this.defaultHeaders.put("Authorization", "Basic " + encodedAuth);
            } else if (connectionParams.containsKey("apiKey")) {
                String apiKey = (String) connectionParams.get("apiKey");
                this.defaultHeaders.put("Authorization", "Bearer " + apiKey);
            }
            
            // 测试连接
            HttpHeaders headers = new HttpHeaders();
            headers.setAll(this.defaultHeaders);
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            ResponseEntity<String> response = restTemplate.exchange(
                baseUrl, HttpMethod.GET, entity, String.class);
            
            connected = response.getStatusCode().is2xxSuccessful();
            return connected;
        } catch (Exception e) {
            throw ExceptionConverter.convertWebException(e);
        }
    }
    
    @Override
    public Result executeOperation(String operationName, Map<String, Object> params) throws AdapterException {
        if (!connected) {
            throw new ConnectionException("Not connected to the REST API");
        }
        
        try {
            // 获取操作配置
            OperationConfig config = getOperationConfig(operationName);
            if (config == null) {
                throw new OperationException("Unknown operation: " + operationName);
            }
            
            // 构建URL
            String url = buildUrl(config.getPath(), params);
            
            // 准备请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setAll(this.defaultHeaders);
            
            // 设置Content-Type
            if (config.getContentType() != null) {
                headers.setContentType(MediaType.parseMediaType(config.getContentType()));
            }
            
            // 准备请求体
            Object body = null;
            if (config.getMethod() == HttpMethod.POST || config.getMethod() == HttpMethod.PUT) {
                body = prepareRequestBody(config.getBodyType(), params);
            }
            
            // 创建请求实体
            HttpEntity<?> requestEntity = new HttpEntity<>(body, headers);
            
            // 执行请求
            ResponseEntity<String> response = restTemplate.exchange(
                url, config.getMethod(), requestEntity, String.class);
            
            // 解析响应
            return parseResponse(response, config);
        } catch (Exception e) {
            throw ExceptionConverter.convertWebException(e);
        }
    }
    
    // 其他方法实现...
    
    /**
     * 构建请求URL
     */
    private String buildUrl(String path, Map<String, Object> params) {
        String url = baseUrl;
        if (!url.endsWith("/") && !path.startsWith("/")) {
            url += "/";
        }
        url += path;
        
        // 替换路径参数
        Pattern pattern = Pattern.compile("\\{(\\w+)\\}");
        Matcher matcher = pattern.matcher(url);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String paramName = matcher.group(1);
            if (params.containsKey(paramName)) {
                String paramValue = params.get(paramName).toString();
                matcher.appendReplacement(sb, paramValue);
                params.remove(paramName); // 从参数集中移除已使用的路径参数
            }
        }
        matcher.appendTail(sb);
        url = sb.toString();
        
        // 添加查询参数
        if (!params.isEmpty() && (HttpMethod.GET.equals(config.getMethod()) || 
                                 HttpMethod.DELETE.equals(config.getMethod()))) {
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                if (!config.getBodyFields().contains(entry.getKey())) {
                    builder.queryParam(entry.getKey(), entry.getValue());
                }
            }
            url = builder.build().encode().toUriString();
        }
        
        return url;
    }
    
    /**
     * 准备请求体
     */
    private Object prepareRequestBody(String bodyType, Map<String, Object> params) {
        if ("json".equalsIgnoreCase(bodyType)) {
            // 提取请求体参数
            Map<String, Object> bodyParams = new HashMap<>();
            for (String field : config.getBodyFields()) {
                if (params.containsKey(field)) {
                    bodyParams.put(field, params.get(field));
                }
            }
            return bodyParams.isEmpty() ? params : bodyParams;
        } else if ("form".equalsIgnoreCase(bodyType)) {
            MultiValueMap<String, String> formParams = new LinkedMultiValueMap<>();
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                formParams.add(entry.getKey(), entry.getValue().toString());
            }
            return formParams;
        } else {
            return params.get("body");
        }
    }
    
    /**
     * 解析响应
     */
    private Result parseResponse(ResponseEntity<String> response, OperationConfig config) throws IOException {
        if (response.getStatusCode().is2xxSuccessful()) {
            String responseBody = response.getBody();
            Map<String, Object> data = new HashMap<>();
            
            if (responseBody != null && !responseBody.isEmpty()) {
                if (response.getHeaders().getContentType() != null && 
                    response.getHeaders().getContentType().includes(MediaType.APPLICATION_JSON)) {
                    // 解析JSON响应
                    ObjectMapper mapper = new ObjectMapper();
                    @SuppressWarnings("unchecked")
                    Map<String, Object> jsonData = mapper.readValue(responseBody, Map.class);
                    data.putAll(jsonData);
                } else {
                    // 非JSON响应作为原始内容返回
                    data.put("content", responseBody);
                }
            }
            
            // 添加响应头信息
            Map<String, Object> headers = new HashMap<>();
            response.getHeaders().forEach((key, value) -> 
                headers.put(key, value.size() == 1 ? value.get(0) : value));
            data.put("headers", headers);
            
            return Result.success(UUID.randomUUID().toString(), data);
        } else {
            return Result.failed("HTTP error: " + response.getStatusCode());
        }
    }
}
```

**2. SOAP服务转换**

SOAP服务转换采用SOAP客户端库进行调用，将统一接口参数转换为SOAP消息，并将SOAP响应转换为标准结果对象。关键转换步骤包括：

- **WSDL解析**：解析WSDL文档，获取服务定义、操作和消息格式。
- **SOAP消息构造**：根据操作名称和参数构造SOAP请求消息。
- **SOAP调用**：发送SOAP请求并接收SOAP响应。
- **响应解析**：解析SOAP响应消息，提取返回值和错误信息。
- **错误处理**：处理SOAP Fault和其他异常情况，转换为统一的异常模型。

SOAP服务适配器的实现与RESTful API适配器类似，但需要处理更复杂的XML消息格式和SOAP协议细节。

#### 3.3.2 Python接口转换

Python接口是科学计算和数据分析领域常用的接口形式。我们采用JEP (Java Embedded Python)或Py4J等技术实现Java与Python的互操作，将统一接口调用转换为Python函数调用。关键转换步骤包括：

- **Python环境初始化**：初始化Python解释器，设置环境变量和导入路径。
- **模块导入**：导入目标Python模块或包。
- **函数调用转换**：将操作名称和参数转换为Python函数调用。
- **数据类型转换**：在Java和Python数据类型之间进行双向转换。
- **异常处理**：捕获并转换Python异常，映射到统一的异常模型。

实现代码示例：

```java
/**
 * Python接口适配器（基于JEP实现）
 */
public class PythonAdapter implements SoftwareAdapter {
    private Interpreter interpreter;
    private String moduleName;
    private boolean connected;
    
    @Override
    public boolean connect(Map<String, Object> connectionParams) throws AdapterException {
        try {
            // 获取模块名称
            this.moduleName = (String) connectionParams.get("moduleName");
            if (moduleName == null) {
                throw new ConnectionException("Module name is required");
            }
            
            // 创建JEP解释器
            SharedInterpreter.setConfig(new JepConfig()
                .addIncludePaths((String) connectionParams.getOrDefault("pythonPath", ""))
                .setRedirectOutputs(true));
            this.interpreter = SharedInterpreter.get();
            
            // 设置Python环境变量
            if (connectionParams.containsKey("environmentVars")) {
                @SuppressWarnings("unchecked")
                Map<String, String> envVars = (Map<String, String>) connectionParams.get("environmentVars");
                for (Map.Entry<String, String> entry : envVars.entrySet()) {
                    interpreter.exec("import os");
                    interpreter.exec("os.environ['" + entry.getKey() + "'] = '" + entry.getValue() + "'");
                }
            }
            
            // 导入模块
            interpreter.exec("import " + moduleName);
            
            connected = true;
            return true;
        } catch (Exception e) {
            throw ExceptionConverter.convertPythonException(e);
        }
    }
    
    @Override
    public Result executeOperation(String operationName, Map<String, Object> params) throws AdapterException {
        if (!connected) {
            throw new ConnectionException("Not connected to Python interpreter");
        }
        
        try {
            // 准备参数
            Object[] args = prepareArguments(operationName, params);
            
            // 执行Python函数
            Object result = interpreter.invoke(moduleName + "." + operationName, args);
            
            // 转换结果
            Map<String, Object> data = convertPythonResult(result);
            
            return Result.success(UUID.randomUUID().toString(), data);
        } catch (Exception e) {
            throw ExceptionConverter.convertPythonException(e);
        }
    }
    
    // 其他方法实现...
    
    /**
     * 准备Python函数参数
     */
    private Object[] prepareArguments(String operationName, Map<String, Object> params) throws AdapterException {
        // 获取函数参数信息
        try {
            interpreter.exec("import inspect");
            String inspectCode = "inspect.signature(" + moduleName + "." + operationName + ")";
            String signature = interpreter.getValue(inspectCode).toString();
            
            // 解析参数签名
            List<String> paramNames = parseParameterNames(signature);
            
            // 准备参数数组
            Object[] args = new Object[paramNames.size()];
            for (int i = 0; i < paramNames.size(); i++) {
                String paramName = paramNames.get(i);
                if (params.containsKey(paramName)) {
                    args[i] = convertJavaToPython(params.get(paramName));
                } else {
                    throw new OperationException("Missing required parameter: " + paramName);
                }
            }
            
            return args;
        } catch (JepException e) {
            throw ExceptionConverter.convertPythonException(e);
        }
    }
    
    /**
     * 解析Python函数参数名
     */
    private List<String> parseParameterNames(String signature) {
        List<String> paramNames = new ArrayList<>();
        
        // 简单解析，实际实现可能需要更复杂的解析逻辑
        int start = signature.indexOf('(');
        int end = signature.lastIndexOf(')');
        if (start >= 0 && end > start) {
            String paramsStr = signature.substring(start + 1, end).trim();
            if (!paramsStr.isEmpty()) {
                String[] params = paramsStr.split(",");
                for (String param : params) {
                    String paramName = param.trim().split("\\s*[:=]\\s*")[0];
                    if (!paramName.isEmpty()) {
                        paramNames.add(paramName);
                    }
                }
            }
        }
        
        return paramNames;
    }
    
    /**
     * 将Java对象转换为Python对象
     */
    private Object convertJavaToPython(Object javaObj) {
        if (javaObj == null) {
            return null;
        } else if (javaObj instanceof Map) {
            // 转换Map为Python字典
            @SuppressWarnings("unchecked")
            Map<Object, Object> javaMap = (Map<Object, Object>) javaObj;
            PyDict pyDict = new PyDict();
            for (Map.Entry<Object, Object> entry : javaMap.entrySet()) {
                pyDict.put(convertJavaToPython(entry.getKey()), 
                          convertJavaToPython(entry.getValue()));
            }
            return pyDict;
        } else if (javaObj instanceof List) {
            // 转换List为Python列表
            @SuppressWarnings("unchecked")
            List<Object> javaList = (List<Object>) javaObj;
            PyList pyList = new PyList();
            for (Object item : javaList) {
                pyList.add(convertJavaToPython(item));
            }
            return pyList;
        } else if (javaObj instanceof Number || javaObj instanceof String || 
                  javaObj instanceof Boolean) {
            // 基本类型可以直接传递
            return javaObj;
        } else {
            // 其他类型转换为字符串
            return javaObj.toString();
        }
    }
    
    /**
     * 将Python结果转换为Java对象
     */
    private Map<String, Object> convertPythonResult(Object pythonResult) {
        Map<String, Object> data = new HashMap<>();
        
        if (pythonResult == null) {
            data.put("result", null);
        } else if (pythonResult instanceof PyObject) {
            // 转换Python对象
            data.put("result", convertPythonToJava((PyObject) pythonResult));
        } else {
            // 直接返回Java对象
            data.put("result", pythonResult);
        }
        
        return data;
    }
    
    /**
     * 将Python对象转换为Java对象
     */
    private Object convertPythonToJava(PyObject pyObj) {
        if (pyObj == null) {
            return null;
        }
        
        try {
            if (pyObj instanceof PyDict) {
                // 转换Python字典为Java Map
                PyDict pyDict = (PyDict) pyObj;
                Map<Object, Object> javaMap = new HashMap<>();
                for (Object key : pyDict.keySet()) {
                    Object javaKey = convertPythonToJava((PyObject) key);
                    Object javaValue = convertPythonToJava(pyDict.get(key));
                    javaMap.put(javaKey, javaValue);
                }
                return javaMap;
            } else if (pyObj instanceof PyList) {
                // 转换Python列表为Java List
                PyList pyList = (PyList) pyObj;
                List<Object> javaList = new ArrayList<>();
                for (Object item : pyList) {
                    javaList.add(convertPythonToJava((PyObject) item));
                }
                return javaList;
            } else if (pyObj instanceof PyCallable) {
                // 不支持转换Python函数
                return pyObj.toString();
            } else {
                // 尝试转换为Java基本类型
                return pyObj.getAttr("__str__").callAs(String.class);
            }
        } catch (Exception e) {
            // 转换失败时返回字符串表示
            return pyObj.toString();
        }
    }
    
    @Override
    public void disconnect() throws AdapterException {
        if (interpreter != null) {
            try {
                interpreter.close();
                connected = false;
            } catch (Exception e) {
                throw ExceptionConverter.convertPythonException(e);
            }
        }
    }
}
```

#### 3.3.3 COM接口转换

COM接口是Windows平台上常见的组件接口形式。我们采用Jacob (Java COM Bridge)或JNA (Java Native Access)技术实现与COM组件的交互，将统一接口调用转换为COM方法调用。关键转换步骤包括：

- **COM环境初始化**：初始化COM环境，设置线程模型。
- **COM组件实例化**：创建COM组件实例，获取接口引用。
- **方法调用转换**：将操作名称和参数转换为COM方法调用。
- **数据类型转换**：在Java和COM数据类型之间进行双向转换。
- **错误处理**：捕获并转换COM错误码和异常，映射到统一的异常模型。

实现代码示例：

```java
/**
 * COM接口适配器（基于Jacob实现）
 */
public class ComAdapter implements SoftwareAdapter {
    private ActiveXComponent component;
    private String progId;
    private boolean connected;
    
    @Override
    public boolean connect(Map<String, Object> connectionParams) throws AdapterException {
        try {
            // 获取COM组件的ProgID
            this.progId = (String) connectionParams.get("progId");
            if (progId == null) {
                throw new ConnectionException("ProgID is required");
            }
            
            // 初始化COM环境
            ComThread.InitSTA();
            
            // 创建COM组件实例
            this.component = new ActiveXComponent(progId);
            
            // 设置可见性（如果适用）
            if (connectionParams.containsKey("visible")) {
                boolean visible = (boolean) connectionParams.get("visible");
                component.setProperty("Visible", new Variant(visible));
            }
            
            connected = true;
            return true;
        } catch (Exception e) {
            throw ExceptionConverter.convertComException(e);
        }
    }
    
    @Override
    public Result executeOperation(String operationName, Map<String, Object> params) throws AdapterException {
        if (!connected) {
            throw new ConnectionException("Not connected to COM component");
        }
        
        try {
            // 准备参数
            Variant[] comParams = prepareComParameters(operationName, params);
            
            // 执行COM方法
            Variant result;
            if (isProperty(operationName, params)) {
                // 属性访问
                if (params.containsKey("value")) {
                    // 设置属性
                    component.setProperty(operationName, new Variant(params.get("value")));
                    result = new Variant(true);
                } else {
                    // 获取属性
                    result = component.getProperty(operationName);
                }
            } else {
                // 方法调用
                result = Dispatch.callN(component, operationName, comParams);
            }
            
            // 转换结果
            Map<String, Object> data = convertComResult(result);
            
            return Result.success(UUID.randomUUID().toString(), data);
        } catch (Exception e) {
            throw ExceptionConverter.convertComException(e);
        }
    }
    
    // 其他方法实现...
    
    /**
     * 判断是否为属性访问
     */
    private boolean isProperty(String operationName, Map<String, Object> params) {
        // 通过参数中的特殊标记或命名约定判断
        return params.containsKey("isProperty") && (boolean) params.get("isProperty");
    }
    
    /**
     * 准备COM方法参数
     */
    private Variant[] prepareComParameters(String operationName, Map<String, Object> params) {
        // 获取参数顺序
        List<String> paramOrder;
        if (params.containsKey("paramOrder")) {
            @SuppressWarnings("unchecked")
            List<String> order = (List<String>) params.get("paramOrder");
            paramOrder = order;
            params.remove("paramOrder"); // 移除特殊参数
        } else {
            // 默认按字母顺序排列参数
            paramOrder = new ArrayList<>(params.keySet());
            Collections.sort(paramOrder);
        }
        
        // 移除特殊参数
        paramOrder.remove("isProperty");
        paramOrder.remove("value");
        
        // 创建COM参数数组
        Variant[] comParams = new Variant[paramOrder.size()];
        for (int i = 0; i < paramOrder.size(); i++) {
            String paramName = paramOrder.get(i);
            Object paramValue = params.get(paramName);
            comParams[i] = convertJavaToCom(paramValue);
        }
        
        return comParams;
    }
    
    /**
     * 将Java对象转换为COM Variant
     */
    private Variant convertJavaToCom(Object javaObj) {
        if (javaObj == null) {
            return new Variant();
        } else if (javaObj instanceof Integer) {
            return new Variant((int) javaObj);
        } else if (javaObj instanceof Double) {
            return new Variant((double) javaObj);
        } else if (javaObj instanceof String) {
            return new Variant((String) javaObj);
        } else if (javaObj instanceof Boolean) {
            return new Variant((boolean) javaObj);
        } else if (javaObj instanceof Date) {
            return new Variant((Date) javaObj);
        } else if (javaObj instanceof byte[]) {
            return new Variant((byte[]) javaObj);
        } else if (javaObj instanceof List) {
            // 转换List为COM SafeArray
            @SuppressWarnings("unchecked")
            List<Object> list = (List<Object>) javaObj;
            SafeArray safeArray = new SafeArray(Variant.VariantVariant, list.size());
            for (int i = 0; i < list.size(); i++) {
                safeArray.setVariant(i, convertJavaToCom(list.get(i)));
            }
            return new Variant(safeArray);
        } else if (javaObj instanceof Map) {
            // 转换Map为COM Dictionary（如果可用）
            try {
                @SuppressWarnings("unchecked")
                Map<Object, Object> map = (Map<Object, Object>) javaObj;
                ActiveXComponent dictionary = new ActiveXComponent("Scripting.Dictionary");
                for (Map.Entry<Object, Object> entry : map.entrySet()) {
                    Dispatch.call(dictionary, "Add", 
                                 convertJavaToCom(entry.getKey()), 
                                 convertJavaToCom(entry.getValue()));
                }
                return new Variant(dictionary.getDispatch());
            } catch (Exception e) {
                // 如果无法创建Dictionary，则返回字符串表示
                return new Variant(javaObj.toString());
            }
        } else {
            // 其他类型转换为字符串
            return new Variant(javaObj.toString());
        }
    }
    
    /**
     * 将COM Variant转换为Java对象
     */
    private Map<String, Object> convertComResult(Variant comResult) {
        Map<String, Object> data = new HashMap<>();
        
        if (comResult == null) {
            data.put("result", null);
        } else {
            // 根据Variant类型转换
            switch (comResult.getvt()) {
                case Variant.VariantEmpty:
                case Variant.VariantNull:
                    data.put("result", null);
                    break;
                case Variant.VariantInt:
                    data.put("result", comResult.getInt());
                    break;
                case Variant.VariantDouble:
                    data.put("result", comResult.getDouble());
                    break;
                case Variant.VariantString:
                    data.put("result", comResult.getString());
                    break;
                case Variant.VariantBoolean:
                    data.put("result", comResult.getBoolean());
                    break;
                case Variant.VariantDate:
                    data.put("result", comResult.getJavaDate());
                    break;
                case Variant.VariantArray:
                    // 转换SafeArray为Java List
                    SafeArray array = comResult.toSafeArray();
                    List<Object> list = new ArrayList<>();
                    for (int i = 0; i < array.size(); i++) {
                        Variant item = array.getVariant(i);
                        Map<String, Object> itemData = convertComResult(item);
                        list.add(itemData.get("result"));
                    }
                    data.put("result", list);
                    break;
                case Variant.VariantDispatch:
                    // 转换IDispatch为字符串表示
                    data.put("result", "COM Object: " + comResult.toString());
                    break;
                default:
                    // 其他类型转换为字符串
                    data.put("result", comResult.toString());
            }
        }
        
        return data;
    }
    
    @Override
    public void disconnect() throws AdapterException {
        if (component != null) {
            try {
                component.safeRelease();
                ComThread.Release();
                connected = false;
            } catch (Exception e) {
                throw ExceptionConverter.convertComException(e);
            }
        }
    }
}
```

#### 3.3.4 本地库接口转换

本地库接口包括C/C++动态链接库等形式。我们采用JNA (Java Native Access)技术实现与本地库的交互，将统一接口调用转换为本地函数调用。关键转换步骤包括：

- **库加载**：加载本地动态链接库。
- **函数映射**：将Java接口映射到本地函数。
- **参数转换**：在Java和本地数据类型之间进行双向转换。
- **内存管理**：处理本地内存分配和释放。
- **错误处理**：捕获并转换本地函数错误，映射到统一的异常模型。

本地库接口适配器的实现较为复杂，需要处理不同平台的差异和内存管理问题，这里不再展示详细代码。
