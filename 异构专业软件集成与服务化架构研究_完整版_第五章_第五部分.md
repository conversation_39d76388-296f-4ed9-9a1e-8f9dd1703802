# 面向异构专业软件的服务化集成架构研究（续）

#### 5.3.3 异步处理技术

异步处理是组件实现的重要技术，特别是对于耗时操作，采用异步处理可以提高系统的响应性和并发处理能力。

```java
/**
 * 异步操作接口
 */
public interface AsyncOperation<T> {
    
    /**
     * 获取操作ID
     * @return 操作ID
     */
    String getOperationId();
    
    /**
     * 获取操作状态
     * @return 操作状态
     */
    OperationStatus getStatus();
    
    /**
     * 获取操作进度
     * @return 操作进度(0-100)
     */
    int getProgress();
    
    /**
     * 获取操作结果
     * @return 操作结果
     * @throws OperationException 操作异常
     */
    T getResult() throws OperationException;
    
    /**
     * 取消操作
     * @return 是否成功取消
     */
    boolean cancel();
    
    /**
     * 操作是否完成
     * @return 是否完成
     */
    boolean isDone();
    
    /**
     * 操作是否取消
     * @return 是否取消
     */
    boolean isCancelled();
    
    /**
     * 等待操作完成
     * @param timeout 超时时间
     * @param unit 时间单位
     * @return 是否完成
     * @throws InterruptedException 中断异常
     */
    boolean await(long timeout, TimeUnit unit) throws InterruptedException;
    
    /**
     * 添加操作监听器
     * @param listener 操作监听器
     */
    void addListener(OperationListener<T> listener);
    
    /**
     * 移除操作监听器
     * @param listener 操作监听器
     */
    void removeListener(OperationListener<T> listener);
}

/**
 * 操作状态枚举
 */
public enum OperationStatus {
    PENDING,    // 等待中
    RUNNING,    // 运行中
    COMPLETED,  // 已完成
    FAILED,     // 失败
    CANCELLED   // 已取消
}

/**
 * 操作异常
 */
public class OperationException extends Exception {
    
    public OperationException(String message) {
        super(message);
    }
    
    public OperationException(String message, Throwable cause) {
        super(message, cause);
    }
}

/**
 * 操作监听器接口
 */
public interface OperationListener<T> {
    
    /**
     * 操作完成回调
     * @param operation 异步操作
     */
    void onCompleted(AsyncOperation<T> operation);
    
    /**
     * 操作失败回调
     * @param operation 异步操作
     * @param exception 异常
     */
    void onFailed(AsyncOperation<T> operation, Throwable exception);
    
    /**
     * 操作取消回调
     * @param operation 异步操作
     */
    void onCancelled(AsyncOperation<T> operation);
    
    /**
     * 操作进度回调
     * @param operation 异步操作
     * @param progress 进度
     */
    void onProgress(AsyncOperation<T> operation, int progress);
}

/**
 * 异步操作实现
 */
public class DefaultAsyncOperation<T> implements AsyncOperation<T> {
    private final String operationId;
    private final CompletableFuture<T> future;
    private final AtomicReference<OperationStatus> status = new AtomicReference<>(OperationStatus.PENDING);
    private final AtomicInteger progress = new AtomicInteger(0);
    private final List<OperationListener<T>> listeners = new CopyOnWriteArrayList<>();
    private final Logger logger = LoggerFactory.getLogger(DefaultAsyncOperation.class);
    
    public DefaultAsyncOperation(String operationId) {
        this.operationId = operationId;
        this.future = new CompletableFuture<>();
        
        // 添加完成回调
        this.future.whenComplete((result, exception) -> {
            if (exception != null) {
                setFailed(exception);
            } else {
                setCompleted(result);
            }
        });
    }
    
    @Override
    public String getOperationId() {
        return operationId;
    }
    
    @Override
    public OperationStatus getStatus() {
        return status.get();
    }
    
    @Override
    public int getProgress() {
        return progress.get();
    }
    
    @Override
    public T getResult() throws OperationException {
        try {
            return future.get();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new OperationException("Operation interrupted", e);
        } catch (ExecutionException e) {
            throw new OperationException("Operation failed", e.getCause());
        }
    }
    
    @Override
    public boolean cancel() {
        if (status.get() == OperationStatus.COMPLETED || 
            status.get() == OperationStatus.FAILED ||
            status.get() == OperationStatus.CANCELLED) {
            return false;
        }
        
        boolean cancelled = future.cancel(true);
        if (cancelled) {
            setCancelled();
        }
        
        return cancelled;
    }
    
    @Override
    public boolean isDone() {
        return future.isDone();
    }
    
    @Override
    public boolean isCancelled() {
        return future.isCancelled();
    }
    
    @Override
    public boolean await(long timeout, TimeUnit unit) throws InterruptedException {
        try {
            future.get(timeout, unit);
            return true;
        } catch (TimeoutException e) {
            return false;
        } catch (ExecutionException e) {
            // 操作失败也算完成
            return true;
        }
    }
    
    @Override
    public void addListener(OperationListener<T> listener) {
        listeners.add(listener);
        
        // 如果操作已经完成，立即通知监听器
        OperationStatus currentStatus = status.get();
        if (currentStatus == OperationStatus.COMPLETED) {
            try {
                T result = future.get();
                listener.onCompleted(this);
            } catch (InterruptedException | ExecutionException e) {
                // 不应该发生
                logger.error("Unexpected error when notifying listener", e);
            }
        } else if (currentStatus == OperationStatus.FAILED) {
            try {
                future.get();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } catch (ExecutionException e) {
                listener.onFailed(this, e.getCause());
            }
        } else if (currentStatus == OperationStatus.CANCELLED) {
            listener.onCancelled(this);
        }
    }
    
    @Override
    public void removeListener(OperationListener<T> listener) {
        listeners.remove(listener);
    }
    
    /**
     * 设置操作进度
     * @param progress 进度
     */
    public void setProgress(int progress) {
        this.progress.set(Math.max(0, Math.min(100, progress)));
        
        // 通知监听器
        for (OperationListener<T> listener : listeners) {
            try {
                listener.onProgress(this, this.progress.get());
            } catch (Exception e) {
                logger.error("Error notifying listener", e);
            }
        }
    }
    
    /**
     * 设置操作开始
     */
    public void setRunning() {
        if (status.compareAndSet(OperationStatus.PENDING, OperationStatus.RUNNING)) {
            logger.debug("Operation started: {}", operationId);
        }
    }
    
    /**
     * 设置操作完成
     * @param result 结果
     */
    public void setCompleted(T result) {
        if (status.compareAndSet(OperationStatus.RUNNING, OperationStatus.COMPLETED)) {
            logger.debug("Operation completed: {}", operationId);
            
            // 设置进度为100%
            setProgress(100);
            
            // 完成Future
            if (!future.isDone()) {
                future.complete(result);
            }
            
            // 通知监听器
            for (OperationListener<T> listener : listeners) {
                try {
                    listener.onCompleted(this);
                } catch (Exception e) {
                    logger.error("Error notifying listener", e);
                }
            }
        }
    }
    
    /**
     * 设置操作失败
     * @param exception 异常
     */
    public void setFailed(Throwable exception) {
        if (status.compareAndSet(OperationStatus.RUNNING, OperationStatus.FAILED)) {
            logger.debug("Operation failed: {}", operationId, exception);
            
            // 完成Future
            if (!future.isDone()) {
                future.completeExceptionally(exception);
            }
            
            // 通知监听器
            for (OperationListener<T> listener : listeners) {
                try {
                    listener.onFailed(this, exception);
                } catch (Exception e) {
                    logger.error("Error notifying listener", e);
                }
            }
        }
    }
    
    /**
     * 设置操作取消
     */
    public void setCancelled() {
        if (status.compareAndSet(OperationStatus.RUNNING, OperationStatus.CANCELLED) ||
            status.compareAndSet(OperationStatus.PENDING, OperationStatus.CANCELLED)) {
            logger.debug("Operation cancelled: {}", operationId);
            
            // 通知监听器
            for (OperationListener<T> listener : listeners) {
                try {
                    listener.onCancelled(this);
                } catch (Exception e) {
                    logger.error("Error notifying listener", e);
                }
            }
        }
    }
}

/**
 * 异步组件操作
 */
public class AsyncComponentOperation<T> implements AsyncOperation<T> {
    private final DefaultAsyncOperation<T> delegate;
    private final Component component;
    private final String operation;
    private final Map<String, Object> params;
    private final ExecutorService executor;
    private final Logger logger = LoggerFactory.getLogger(AsyncComponentOperation.class);
    
    public AsyncComponentOperation(Component component, String operation, Map<String, Object> params, ExecutorService executor) {
        this.delegate = new DefaultAsyncOperation<>(UUID.randomUUID().toString());
        this.component = component;
        this.operation = operation;
        this.params = params;
        this.executor = executor;
    }
    
    /**
     * 启动异步操作
     */
    public void start() {
        delegate.setRunning();
        
        executor.submit(() -> {
            try {
                // 执行组件操作
                ComponentResult result = component.execute(operation, params);
                
                // 处理结果
                if (result.isSuccess()) {
                    @SuppressWarnings("unchecked")
                    T data = (T) result.getData();
                    delegate.setCompleted(data);
                } else {
                    delegate.setFailed(new OperationException(result.getMessage()));
                }
            } catch (Exception e) {
                delegate.setFailed(e);
            }
        });
    }
    
    @Override
    public String getOperationId() {
        return delegate.getOperationId();
    }
    
    @Override
    public OperationStatus getStatus() {
        return delegate.getStatus();
    }
    
    @Override
    public int getProgress() {
        return delegate.getProgress();
    }
    
    @Override
    public T getResult() throws OperationException {
        return delegate.getResult();
    }
    
    @Override
    public boolean cancel() {
        return delegate.cancel();
    }
    
    @Override
    public boolean isDone() {
        return delegate.isDone();
    }
    
    @Override
    public boolean isCancelled() {
        return delegate.isCancelled();
    }
    
    @Override
    public boolean await(long timeout, TimeUnit unit) throws InterruptedException {
        return delegate.await(timeout, unit);
    }
    
    @Override
    public void addListener(OperationListener<T> listener) {
        delegate.addListener(listener);
    }
    
    @Override
    public void removeListener(OperationListener<T> listener) {
        delegate.removeListener(listener);
    }
    
    /**
     * 更新进度
     * @param progress 进度
     */
    public void updateProgress(int progress) {
        delegate.setProgress(progress);
    }
}
```

异步处理技术考虑了以下关键点：

- **异步操作接口**：定义统一的异步操作接口，支持状态查询、结果获取和取消操作等功能。
- **进度跟踪**：支持操作进度的跟踪和通知，便于监控长时间运行的操作。
- **监听器机制**：提供监听器机制，支持异步操作完成、失败和取消等事件的通知。
- **超时控制**：支持等待操作完成的超时控制，避免无限等待。
- **异常处理**：处理异步操作中的异常，确保异常信息能够正确传递给调用者。

#### 5.3.4 缓存优化技术

缓存优化是提高组件性能的重要技术，通过缓存频繁使用的数据和计算结果，减少重复计算和资源消耗。

```java
/**
 * 缓存接口
 */
public interface Cache<K, V> {
    
    /**
     * 获取缓存值
     * @param key 缓存键
     * @return 缓存值，如果不存在则返回null
     */
    V get(K key);
    
    /**
     * 获取缓存值，如果不存在则计算
     * @param key 缓存键
     * @param loader 值加载器
     * @return 缓存值
     * @throws CacheException 缓存异常
     */
    V getOrCompute(K key, Supplier<V> loader) throws CacheException;
    
    /**
     * 放入缓存
     * @param key 缓存键
     * @param value 缓存值
     */
    void put(K key, V value);
    
    /**
     * 放入缓存，带过期时间
     * @param key 缓存键
     * @param value 缓存值
     * @param expireAfter 过期时间
     * @param unit 时间单位
     */
    void put(K key, V value, long expireAfter, TimeUnit unit);
    
    /**
     * 移除缓存
     * @param key 缓存键
     * @return 是否存在并移除
     */
    boolean remove(K key);
    
    /**
     * 清空缓存
     */
    void clear();
    
    /**
     * 获取缓存大小
     * @return 缓存大小
     */
    int size();
    
    /**
     * 获取缓存统计信息
     * @return 缓存统计信息
     */
    CacheStats getStats();
}

/**
 * 缓存异常
 */
public class CacheException extends Exception {
    
    public CacheException(String message) {
        super(message);
    }
    
    public CacheException(String message, Throwable cause) {
        super(message, cause);
    }
}

/**
 * 缓存统计信息
 */
public class CacheStats {
    private final long hitCount;
    private final long missCount;
    private final long loadSuccessCount;
    private final long loadFailureCount;
    private final long totalLoadTime;
    private final long evictionCount;
    
    public CacheStats(long hitCount, long missCount, long loadSuccessCount, 
                     long loadFailureCount, long totalLoadTime, long evictionCount) {
        this.hitCount = hitCount;
        this.missCount = missCount;
        this.loadSuccessCount = loadSuccessCount;
        this.loadFailureCount = loadFailureCount;
        this.totalLoadTime = totalLoadTime;
        this.evictionCount = evictionCount;
    }
    
    /**
     * 获取命中率
     * @return 命中率
     */
    public double hitRate() {
        long requestCount = hitCount + missCount;
        return requestCount == 0 ? 1.0 : (double) hitCount / requestCount;
    }
    
    /**
     * 获取加载成功率
     * @return 加载成功率
     */
    public double loadSuccessRate() {
        long loadCount = loadSuccessCount + loadFailureCount;
        return loadCount == 0 ? 1.0 : (double) loadSuccessCount / loadCount;
    }
    
    /**
     * 获取平均加载时间
     * @return 平均加载时间(毫秒)
     */
    public double averageLoadPenalty() {
        return loadSuccessCount == 0 ? 0.0 : (double) totalLoadTime / loadSuccessCount;
    }
    
    // getters...
}

/**
 * 内存缓存实现
 */
public class MemoryCache<K, V> implements Cache<K, V> {
    private final ConcurrentMap<K, CacheEntry<V>> cache = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private final AtomicLong hitCount = new AtomicLong();
    private final AtomicLong missCount = new AtomicLong();
    private final AtomicLong loadSuccessCount = new AtomicLong();
    private final AtomicLong loadFailureCount = new AtomicLong();
    private final AtomicLong totalLoadTime = new AtomicLong();
    private final AtomicLong evictionCount = new AtomicLong();
    private final int maxSize;
    private final Logger logger = LoggerFactory.getLogger(MemoryCache.class);
    
    public MemoryCache(int maxSize) {
        this.maxSize = maxSize;
        
        // 启动定期清理任务
        scheduler.scheduleAtFixedRate(this::cleanupExpiredEntries, 1, 1, TimeUnit.MINUTES);
    }
    
    @Override
    public V get(K key) {
        CacheEntry<V> entry = cache.get(key);
        
        if (entry == null) {
            missCount.incrementAndGet();
            return null;
        }
        
        if (entry.isExpired()) {
            cache.remove(key);
            missCount.incrementAndGet();
            return null;
        }
        
        hitCount.incrementAndGet();
        return entry.getValue();
    }
    
    @Override
    public V getOrCompute(K key, Supplier<V> loader) throws CacheException {
        V value = get(key);
        
        if (value != null) {
            return value;
        }
        
        try {
            long startTime = System.currentTimeMillis();
            value = loader.get();
            long loadTime = System.currentTimeMillis() - startTime;
            
            if (value != null) {
                put(key, value);
                loadSuccessCount.incrementAndGet();
                totalLoadTime.addAndGet(loadTime);
            }
            
            return value;
        } catch (Exception e) {
            loadFailureCount.incrementAndGet();
            throw new CacheException("Failed to load value for key: " + key, e);
        }
    }
    
    @Override
    public void put(K key, V value) {
        put(key, value, 0, TimeUnit.MILLISECONDS);
    }
    
    @Override
    public void put(K key, V value, long expireAfter, TimeUnit unit) {
        if (value == null) {
            return;
        }
        
        // 检查缓存大小
        if (cache.size() >= maxSize) {
            evictEntries();
        }
        
        long expireTime = expireAfter > 0 ? 
                System.currentTimeMillis() + unit.toMillis(expireAfter) : 0;
        
        cache.put(key, new CacheEntry<>(value, expireTime));
    }
    
    @Override
    public boolean remove(K key) {
        return cache.remove(key) != null;
    }
    
    @Override
    public void clear() {
        cache.clear();
    }
    
    @Override
    public int size() {
        return cache.size();
    }
    
    @Override
    public CacheStats getStats() {
        return new CacheStats(
            hitCount.get(),
            missCount.get(),
            loadSuccessCount.get(),
            loadFailureCount.get(),
            totalLoadTime.get(),
            evictionCount.get()
        );
    }
    
    /**
     * 清理过期条目
     */
    private void cleanupExpiredEntries() {
        try {
            int count = 0;
            long now = System.currentTimeMillis();
            
            for (Iterator<Map.Entry<K, CacheEntry<V>>> it = cache.entrySet().iterator(); it.hasNext();) {
                Map.Entry<K, CacheEntry<V>> entry = it.next();
                if (entry.getValue().isExpired(now)) {
                    it.remove();
                    count++;
                }
            }
            
            if (count > 0) {
                evictionCount.addAndGet(count);
                logger.debug("Cleaned up {} expired cache entries", count);
            }
        } catch (Exception e) {
            logger.error("Error cleaning up expired cache entries", e);
        }
    }
    
    /**
     * 驱逐条目
     */
    private void evictEntries() {
        // 简单的策略：移除10%的条目
        int toRemove = Math.max(1, cache.size() / 10);
        
        // 随机选择条目移除
        List<K> keys = new ArrayList<>(cache.keySet());
        Collections.shuffle(keys);
        
        int count = 0;
        for (int i = 0; i < toRemove && i < keys.size(); i++) {
            if (cache.remove(keys.get(i)) != null) {
                count++;
            }
        }
        
        evictionCount.addAndGet(count);
        logger.debug("Evicted {} cache entries", count);
    }
    
    /**
     * 缓存条目
     */
    private static class CacheEntry<V> {
        private final V value;
        private final long expireTime;
        
        public CacheEntry(V value, long expireTime) {
            this.value = value;
            this.expireTime = expireTime;
        }
        
        public V getValue() {
            return value;
        }
        
        public boolean isExpired() {
            return isExpired(System.currentTimeMillis());
        }
        
        public boolean isExpired(long now) {
            return expireTime > 0 && now > expireTime;
        }
    }
}

/**
 * 组件结果缓存
 */
public class ComponentResultCache {
    private final Cache<CacheKey, ComponentResult> cache;
    private final Logger logger = LoggerFactory.getLogger(ComponentResultCache.class);
    
    public ComponentResultCache(int maxSize) {
        this.cache = new MemoryCache<>(maxSize);
    }
    
    /**
     * 获取缓存结果
     * @param componentId 组件ID
     * @param operation 操作名称
     * @param params 操作参数
     * @return 缓存结果，如果不存在则返回null
     */
    public ComponentResult get(String componentId, String operation, Map<String, Object> params) {
        CacheKey key = new CacheKey(componentId, operation, params);
        return cache.get(key);
    }
    
    /**
     * 缓存结果
     * @param componentId 组件ID
     * @param operation 操作名称
     * @param params 操作参数
     * @param result 操作结果
     * @param expireAfter 过期时间
     * @param unit 时间单位
     */
    public void put(String componentId, String operation, Map<String, Object> params, 
                   ComponentResult result, long expireAfter, TimeUnit unit) {
        if (result == null || !result.isSuccess()) {
            return;
        }
        
        CacheKey key = new CacheKey(componentId, operation, params);
        cache.put(key, result, expireAfter, unit);
        
        logger.debug("Cached result for component: {}, operation: {}", componentId, operation);
    }
    
    /**
     * 移除缓存结果
     * @param componentId 组件ID
     * @param operation 操作名称
     * @param params 操作参数
     * @return 是否存在并移除
     */
    public boolean remove(String componentId, String operation, Map<String, Object> params) {
        CacheKey key = new CacheKey(componentId, operation, params);
        return cache.remove(key);
    }
    
    /**
     * 清空组件的缓存结果
     * @param componentId 组件ID
     */
    public void clearComponent(String componentId) {
        // 遍历缓存，移除指定组件的条目
        // 简化实现，实际可能需要更高效的方式
        for (CacheKey key : new ArrayList<>(((MemoryCache<CacheKey, ComponentResult>) cache).getKeys())) {
            if (key.getComponentId().equals(componentId)) {
                cache.remove(key);
            }
        }
        
        logger.debug("Cleared cache for component: {}", componentId);
    }
    
    /**
     * 清空所有缓存结果
     */
    public void clear() {
        cache.clear();
        logger.debug("Cleared all cache");
    }
    
    /**
     * 获取缓存统计信息
     * @return 缓存统计信息
     */
    public CacheStats getStats() {
        return cache.getStats();
    }
    
    /**
     * 缓存键
     */
    private static class CacheKey {
        private final String componentId;
        private final String operation;
        private final Map<String, Object> params;
        
        public CacheKey(String componentId, String operation, Map<String, Object> params) {
            this.componentId = componentId;
            this.operation = operation;
            this.params = new HashMap<>(params);
        }
        
        public String getComponentId() {
            return componentId;
        }
        
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            CacheKey cacheKey = (CacheKey) o;
            return Objects.equals(componentId, cacheKey.componentId) &&
                   Objects.equals(operation, cacheKey.operation) &&
                   Objects.equals(params, cacheKey.params);
        }
        
        @Override
        public int hashCode() {
            return Objects.hash(componentId, operation, params);
        }
    }
}
```

缓存优化技术考虑了以下关键点：

- **缓存接口**：定义统一的缓存接口，支持缓存的获取、存储和移除等操作。
- **过期机制**：支持缓存条目的过期时间设置，自动清理过期条目。
- **驱逐策略**：当缓存达到最大容量时，实现合理的驱逐策略，移除不常用的条目。
- **统计信息**：收集缓存使用的统计信息，如命中率、加载时间等，便于监控和优化。
- **组件结果缓存**：专门为组件操作结果设计的缓存，提高组件操作的响应速度。
